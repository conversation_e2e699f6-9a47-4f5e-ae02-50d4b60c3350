using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace VbDeobf.AST;

/// <summary>
/// Utility class for AST manipulation and analysis
/// </summary>
public static class ASTUtilities
{
    /// <summary>
    /// Find all nodes of a specific type in the AST
    /// </summary>
    /// <typeparam name="T">Type of nodes to find</typeparam>
    /// <param name="root">Root node to search from</param>
    /// <returns>All nodes of the specified type</returns>
    public static IEnumerable<T> FindNodesOfType<T>(IASTNode root) where T : class, IASTNode
    {
        if (root is T result)
        {
            yield return result;
        }

        foreach (var child in root.Children)
        {
            foreach (var descendant in FindNodesOfType<T>(child))
            {
                yield return descendant;
            }
        }
    }

    /// <summary>
    /// Find the first node of a specific type in the AST
    /// </summary>
    /// <typeparam name="T">Type of node to find</typeparam>
    /// <param name="root">Root node to search from</param>
    /// <returns>First node of the specified type, or null if not found</returns>
    public static T? FindFirstNodeOfType<T>(IASTNode root) where T : class, IASTNode
    {
        return FindNodesOfType<T>(root).FirstOrDefault();
    }

    /// <summary>
    /// Get all identifiers used in the AST
    /// </summary>
    /// <param name="root">Root node to search from</param>
    /// <returns>All identifier names</returns>
    public static IEnumerable<string> GetAllIdentifiers(IASTNode root)
    {
        var identifierNodes = FindNodesOfType<IdentifierExpressionNode>(root);
        var identifierDecls = FindNodesOfType<IdentifierNode>(root);
        var declarations = FindNodesOfType<DeclarationNode>(root);

        foreach (var node in identifierNodes)
        {
            yield return node.Name;
        }

        foreach (var node in identifierDecls)
        {
            yield return node.Name;
        }

        foreach (var node in declarations)
        {
            yield return node.Name;
        }
    }

    /// <summary>
    /// Get all function and subroutine declarations
    /// </summary>
    /// <param name="root">Root node to search from</param>
    /// <returns>All procedure declarations</returns>
    public static IEnumerable<DeclarationNode> GetAllProcedures(IASTNode root)
    {
        return FindNodesOfType<FunctionDeclarationNode>(root).Cast<DeclarationNode>()
            .Concat(FindNodesOfType<SubroutineDeclarationNode>(root).Cast<DeclarationNode>())
            .Concat(FindNodesOfType<PropertyDeclarationNode>(root).Cast<DeclarationNode>());
    }

    /// <summary>
    /// Get all variable declarations
    /// </summary>
    /// <param name="root">Root node to search from</param>
    /// <returns>All variable declarations</returns>
    public static IEnumerable<VariableDeclarationNode> GetAllVariables(IASTNode root)
    {
        return FindNodesOfType<VariableDeclarationNode>(root);
    }

    /// <summary>
    /// Get all constant declarations
    /// </summary>
    /// <param name="root">Root node to search from</param>
    /// <returns>All constant declarations</returns>
    public static IEnumerable<ConstantDeclarationNode> GetAllConstants(IASTNode root)
    {
        return FindNodesOfType<ConstantDeclarationNode>(root);
    }

    /// <summary>
    /// Replace all occurrences of an identifier with a new name
    /// </summary>
    /// <param name="root">Root node to search in</param>
    /// <param name="oldName">Old identifier name</param>
    /// <param name="newName">New identifier name</param>
    /// <returns>Number of replacements made</returns>
    public static int ReplaceIdentifier(IASTNode root, string oldName, string newName)
    {
        var count = 0;

        // Replace in identifier expressions
        foreach (var node in FindNodesOfType<IdentifierExpressionNode>(root))
        {
            if (string.Equals(node.Name, oldName, StringComparison.OrdinalIgnoreCase))
            {
                node.Name = newName;
                count++;
            }
        }

        // Replace in identifier nodes
        foreach (var node in FindNodesOfType<IdentifierNode>(root))
        {
            if (string.Equals(node.Name, oldName, StringComparison.OrdinalIgnoreCase))
            {
                node.Name = newName;
                count++;
            }
        }

        // Replace in declarations
        foreach (var node in FindNodesOfType<DeclarationNode>(root))
        {
            if (string.Equals(node.Name, oldName, StringComparison.OrdinalIgnoreCase))
            {
                node.Name = newName;
                count++;
            }
        }

        return count;
    }

    /// <summary>
    /// Calculate the depth of the AST
    /// </summary>
    /// <param name="root">Root node</param>
    /// <returns>Maximum depth of the AST</returns>
    public static int CalculateDepth(IASTNode root)
    {
        return root.Children.Count == 0 ? 1 : 1 + root.Children.Max(child => CalculateDepth(child));
    }

    /// <summary>
    /// Count total number of nodes in the AST
    /// </summary>
    /// <param name="root">Root node</param>
    /// <returns>Total number of nodes</returns>
    public static int CountNodes(IASTNode root)
    {
        return 1 + root.Children.Sum(child => CountNodes(child));
    }

    /// <summary>
    /// Get statistics about the AST
    /// </summary>
    /// <param name="root">Root node</param>
    /// <returns>AST statistics</returns>
    public static ASTStatistics GetStatistics(IASTNode root)
    {
        var stats = new ASTStatistics
        {
            TotalNodes = CountNodes(root),
            MaxDepth = CalculateDepth(root),
            FunctionCount = FindNodesOfType<FunctionDeclarationNode>(root).Count(),
            SubroutineCount = FindNodesOfType<SubroutineDeclarationNode>(root).Count(),
            PropertyCount = FindNodesOfType<PropertyDeclarationNode>(root).Count(),
            VariableCount = FindNodesOfType<VariableDeclarationNode>(root).Count(),
            ConstantCount = FindNodesOfType<ConstantDeclarationNode>(root).Count(),
            IfStatementCount = FindNodesOfType<IfStatementNode>(root).Count(),
            ForLoopCount = FindNodesOfType<ForStatementNode>(root).Count(),
            WhileLoopCount = FindNodesOfType<WhileStatementNode>(root).Count(),
            DoLoopCount = FindNodesOfType<DoLoopStatementNode>(root).Count(),
            CallStatementCount = FindNodesOfType<CallStatementNode>(root).Count(),
            AssignmentCount = FindNodesOfType<AssignmentStatementNode>(root).Count()
        };

        var identifiers = GetAllIdentifiers(root).ToList();
        stats.UniqueIdentifierCount = identifiers.Distinct(StringComparer.OrdinalIgnoreCase).Count();
        stats.TotalIdentifierCount = identifiers.Count;

        return stats;
    }

    /// <summary>
    /// Clone an AST node (deep copy)
    /// </summary>
    /// <param name="node">Node to clone</param>
    /// <returns>Cloned node</returns>
    public static T CloneNode<T>(T node) where T : IASTNode
    {
        // This is a simplified implementation
        // In a real implementation, you'd need to handle all node types properly
        throw new NotImplementedException("Node cloning not yet implemented");
    }

    /// <summary>
    /// Validate the AST structure
    /// </summary>
    /// <param name="root">Root node to validate</param>
    /// <returns>List of validation errors</returns>
    public static List<string> ValidateAST(IASTNode root)
    {
        var errors = new List<string>();

        ValidateNode(root, errors, new HashSet<IASTNode>());

        return errors;
    }

    private static void ValidateNode(IASTNode node, List<string> errors, HashSet<IASTNode> visited)
    {
        if (visited.Contains(node))
        {
            errors.Add($"Circular reference detected in node {node.GetType().Name}");
            return;
        }

        visited.Add(node);

        // Check parent-child relationships
        foreach (var child in node.Children)
        {
            if (child.Parent != node)
            {
                errors.Add($"Child node {child.GetType().Name} has incorrect parent reference");
            }

            ValidateNode(child, errors, visited);
        }

        visited.Remove(node);
    }
}

/// <summary>
/// Statistics about an AST
/// </summary>
public class ASTStatistics
{
    public int TotalNodes { get; set; }
    public int MaxDepth { get; set; }
    public int FunctionCount { get; set; }
    public int SubroutineCount { get; set; }
    public int PropertyCount { get; set; }
    public int VariableCount { get; set; }
    public int ConstantCount { get; set; }
    public int IfStatementCount { get; set; }
    public int ForLoopCount { get; set; }
    public int WhileLoopCount { get; set; }
    public int DoLoopCount { get; set; }
    public int CallStatementCount { get; set; }
    public int AssignmentCount { get; set; }
    public int UniqueIdentifierCount { get; set; }
    public int TotalIdentifierCount { get; set; }

    public override string ToString()
    {
        var sb = new StringBuilder();
        sb.AppendLine("AST Statistics:");
        sb.AppendLine($"  Total Nodes: {this.TotalNodes}");
        sb.AppendLine($"  Max Depth: {this.MaxDepth}");
        sb.AppendLine($"  Functions: {this.FunctionCount}");
        sb.AppendLine($"  Subroutines: {this.SubroutineCount}");
        sb.AppendLine($"  Properties: {this.PropertyCount}");
        sb.AppendLine($"  Variables: {this.VariableCount}");
        sb.AppendLine($"  Constants: {this.ConstantCount}");
        sb.AppendLine($"  If Statements: {this.IfStatementCount}");
        sb.AppendLine($"  For Loops: {this.ForLoopCount}");
        sb.AppendLine($"  While Loops: {this.WhileLoopCount}");
        sb.AppendLine($"  Do Loops: {this.DoLoopCount}");
        sb.AppendLine($"  Call Statements: {this.CallStatementCount}");
        sb.AppendLine($"  Assignments: {this.AssignmentCount}");
        sb.AppendLine($"  Unique Identifiers: {this.UniqueIdentifierCount}");
        sb.AppendLine($"  Total Identifiers: {this.TotalIdentifierCount}");
        return sb.ToString();
    }
}