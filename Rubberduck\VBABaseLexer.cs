﻿using Antlr4.Runtime;
public abstract class VBABaseLexer : Lexer
{
    public VBABaseLexer(ICharStream input) : base(input) { }
    public VBABaseLexer(ICharStream input, TextWriter output, TextWriter errorOutput)
    : base(input, output, errorOutput) { }

    #region Semantic predicate helper

    protected int CharAtRelativePosition(int i)
    {
        return base.InputStream.LA(i);
    }

    protected bool IsChar(int actual, char expected)
    {
        return (char) actual == expected;
    }

    protected bool IsChar(int actual, params char[] expectedOptions)
    {
        var actualAsChar = (char) actual;
        foreach (var expected in expectedOptions)
        {
            if (actualAsChar == expected)
            {
                return true;
            }
        }
        return false;
    }
    #endregion
}