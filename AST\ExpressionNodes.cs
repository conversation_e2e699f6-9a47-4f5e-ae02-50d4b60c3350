using System.Collections.Generic;

namespace VbDeobf.AST;

/// <summary>
/// Literal expression node
/// </summary>
public class LiteralExpressionNode : ExpressionNode
{
    public override ASTNodeType NodeType => ASTNodeType.LiteralExpression;
    public object? Value { get; set; }
    public LiteralType LiteralType { get; set; }

    public LiteralExpressionNode(object? value, LiteralType literalType, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Value = value;
        this.LiteralType = literalType;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitLiteralExpression(this);
    }
}

/// <summary>
/// Identifier expression node
/// </summary>
public class IdentifierExpressionNode : ExpressionNode
{
    public override ASTNodeType NodeType => ASTNodeType.IdentifierExpression;
    public string Name { get; set; }

    public IdentifierExpressionNode(string name, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.Name = name;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitIdentifierExpression(this);
    }
}

/// <summary>
/// Binary expression node
/// </summary>
public class BinaryExpressionNode : ExpressionNode
{
    public override ASTNodeType NodeType => ASTNodeType.BinaryExpression;
    public ExpressionNode Left { get; set; }
    public BinaryOperator Operator { get; set; }
    public ExpressionNode Right { get; set; }

    public BinaryExpressionNode(ExpressionNode left, BinaryOperator op, ExpressionNode right, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Left = left;
        this.Operator = op;
        this.Right = right;
        this.AddChild(left);
        this.AddChild(right);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitBinaryExpression(this);
    }
}

/// <summary>
/// Unary expression node
/// </summary>
public class UnaryExpressionNode : ExpressionNode
{
    public override ASTNodeType NodeType => ASTNodeType.UnaryExpression;
    public UnaryOperator Operator { get; set; }
    public ExpressionNode Operand { get; set; }

    public UnaryExpressionNode(UnaryOperator op, ExpressionNode operand, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Operator = op;
        this.Operand = operand;
        this.AddChild(operand);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitUnaryExpression(this);
    }
}

/// <summary>
/// Member access expression node (e.g., obj.member)
/// </summary>
public class MemberAccessExpressionNode : ExpressionNode
{
    public override ASTNodeType NodeType => ASTNodeType.MemberAccessExpression;
    public ExpressionNode Object { get; set; }
    public string MemberName { get; set; }
    public bool IsDictionaryAccess { get; set; } // For obj!member syntax

    public MemberAccessExpressionNode(ExpressionNode obj, string memberName, bool isDictionaryAccess = false, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Object = obj;
        this.MemberName = memberName;
        this.IsDictionaryAccess = isDictionaryAccess;
        this.AddChild(obj);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitMemberAccessExpression(this);
    }
}

/// <summary>
/// Index expression node (e.g., arr(1, 2))
/// </summary>
public class IndexExpressionNode : ExpressionNode
{
    public override ASTNodeType NodeType => ASTNodeType.IndexExpression;
    public ExpressionNode Object { get; set; }
    public ArgumentListNode Arguments { get; set; }

    public IndexExpressionNode(ExpressionNode obj, ArgumentListNode arguments, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Object = obj;
        this.Arguments = arguments;
        this.AddChild(obj);
        this.AddChild(arguments);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitIndexExpression(this);
    }
}

/// <summary>
/// Call expression node (e.g., func(arg1, arg2))
/// </summary>
public class CallExpressionNode : ExpressionNode
{
    public override ASTNodeType NodeType => ASTNodeType.CallExpression;
    public ExpressionNode Function { get; set; }
    public ArgumentListNode Arguments { get; set; }

    public CallExpressionNode(ExpressionNode function, ArgumentListNode arguments, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Function = function;
        this.Arguments = arguments;
        this.AddChild(function);
        this.AddChild(arguments);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitCallExpression(this);
    }
}

/// <summary>
/// Parenthesized expression node
/// </summary>
public class ParenthesizedExpressionNode : ExpressionNode
{
    public override ASTNodeType NodeType => ASTNodeType.ParenthesizedExpression;
    public ExpressionNode Expression { get; set; }

    public ParenthesizedExpressionNode(ExpressionNode expression, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Expression = expression;
        this.AddChild(expression);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitParenthesizedExpression(this);
    }
}

/// <summary>
/// TypeOf expression node
/// </summary>
public class TypeOfExpressionNode : ExpressionNode
{
    public override ASTNodeType NodeType => ASTNodeType.TypeOfExpression;
    public ExpressionNode Expression { get; set; }

    public TypeOfExpressionNode(ExpressionNode expression, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Expression = expression;
        this.AddChild(expression);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitTypeOfExpression(this);
    }
}

/// <summary>
/// New expression node
/// </summary>
public class NewExpressionNode : ExpressionNode
{
    public override ASTNodeType NodeType => ASTNodeType.NewExpression;
    public ExpressionNode Type { get; set; }

    public NewExpressionNode(ExpressionNode type, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Type = type;
        this.AddChild(type);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitNewExpression(this);
    }
}

/// <summary>
/// AddressOf expression node
/// </summary>
public class AddressOfExpressionNode : ExpressionNode
{
    public override ASTNodeType NodeType => ASTNodeType.AddressOfExpression;
    public ExpressionNode Expression { get; set; }

    public AddressOfExpressionNode(ExpressionNode expression, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Expression = expression;
        this.AddChild(expression);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitAddressOfExpression(this);
    }
}

/// <summary>
/// Types of literals
/// </summary>
public enum LiteralType
{
    String,
    Number,
    Boolean,
    Date,
    Nothing,
    Empty,
    Null
}