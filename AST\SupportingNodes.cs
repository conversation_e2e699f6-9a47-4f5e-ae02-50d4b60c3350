using System.Collections.Generic;

namespace VbDeobf.AST;

/// <summary>
/// Parameter node
/// </summary>
public class ParameterNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.Parameter;
    public string Name { get; set; }
    public TypeNode? Type { get; set; }
    public ExpressionNode? DefaultValue { get; set; }
    public ParameterModifier Modifier { get; set; } = ParameterModifier.None;
    public bool IsOptional { get; set; }
    public bool IsParamArray { get; set; }

    public ParameterNode(string name, TypeNode? type = null, ExpressionNode? defaultValue = null, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Name = name;
        this.Type = type;
        this.DefaultValue = defaultValue;
        if (type != null)
        {
            this.AddChild(type);
        }

        if (defaultValue != null)
        {
            this.AddChild(defaultValue);
        }
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitParameter(this);
    }
}

/// <summary>
/// Parameter list node
/// </summary>
public class ParameterListNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.ParameterList;
    public List<ParameterNode> Parameters { get; set; } = new List<ParameterNode>();

    public ParameterListNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }

    public void AddParameter(ParameterNode parameter)
    {
        this.Parameters.Add(parameter);
        this.AddChild(parameter);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitParameterList(this);
    }
}

/// <summary>
/// Argument node
/// </summary>
public class ArgumentNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.Argument;
    public ExpressionNode Expression { get; set; }
    public string? Name { get; set; } // For named arguments
    public bool IsByVal { get; set; }
    public bool IsMissing { get; set; }

    public ArgumentNode(ExpressionNode expression, string? name = null, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Expression = expression;
        this.Name = name;
        this.AddChild(expression);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitArgument(this);
    }
}

/// <summary>
/// Argument list node
/// </summary>
public class ArgumentListNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.ArgumentList;
    public List<ArgumentNode> Arguments { get; set; } = new List<ArgumentNode>();

    public ArgumentListNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }

    public void AddArgument(ArgumentNode argument)
    {
        this.Arguments.Add(argument);
        this.AddChild(argument);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitArgumentList(this);
    }
}

/// <summary>
/// Base type node
/// </summary>
public abstract class TypeNode : ASTNodeBase
{
    protected TypeNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }
}

/// <summary>
/// Built-in type node
/// </summary>
public class BuiltInTypeNode : TypeNode
{
    public override ASTNodeType NodeType => ASTNodeType.BuiltInType;
    public VBAType Type { get; set; }

    public BuiltInTypeNode(VBAType type, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.Type = type;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitBuiltInType(this);
    }
}

/// <summary>
/// User-defined type node
/// </summary>
public class UserDefinedTypeNode : TypeNode
{
    public override ASTNodeType NodeType => ASTNodeType.UserDefinedType;
    public string TypeName { get; set; }

    public UserDefinedTypeNode(string typeName, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.TypeName = typeName;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitUserDefinedType(this);
    }
}

/// <summary>
/// Array type node
/// </summary>
public class ArrayTypeNode : TypeNode
{
    public override ASTNodeType NodeType => ASTNodeType.ArrayType;
    public TypeNode? ElementType { get; set; }
    public List<ArrayDimensionNode> Dimensions { get; set; } = new List<ArrayDimensionNode>();

    public ArrayTypeNode(TypeNode? elementType, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.ElementType = elementType;
        if (elementType != null)
        {
            this.AddChild(elementType);
        }
    }

    public void AddDimension(ArrayDimensionNode dimension)
    {
        this.Dimensions.Add(dimension);
        this.AddChild(dimension);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitArrayType(this);
    }
}

/// <summary>
/// Attribute node
/// </summary>
public class AttributeNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.Attribute;
    public string Name { get; set; }
    public List<ExpressionNode> Arguments { get; set; } = new List<ExpressionNode>();

    public AttributeNode(string name, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.Name = name;
    }

    public void AddArgument(ExpressionNode argument)
    {
        this.Arguments.Add(argument);
        this.AddChild(argument);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitAttribute(this);
    }
}

/// <summary>
/// Attribute list node
/// </summary>
public class AttributeListNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.AttributeList;
    public List<AttributeNode> Attributes { get; set; } = new List<AttributeNode>();

    public AttributeListNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }

    public void AddAttribute(AttributeNode attribute)
    {
        this.Attributes.Add(attribute);
        this.AddChild(attribute);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitAttributeList(this);
    }
}

/// <summary>
/// Visibility node
/// </summary>
public class VisibilityNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.Visibility;
    public VisibilityModifier Visibility { get; set; }

    public VisibilityNode(VisibilityModifier visibility, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.Visibility = visibility;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitVisibility(this);
    }
}

/// <summary>
/// Block node
/// </summary>
public class BlockNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.Block;
    public List<StatementNode> Statements { get; set; } = new List<StatementNode>();

    public BlockNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }

    public void AddStatement(StatementNode statement)
    {
        this.Statements.Add(statement);
        this.AddChild(statement);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitBlock(this);
    }
}

/// <summary>
/// Identifier node
/// </summary>
public class IdentifierNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.Identifier;
    public string Name { get; set; }

    public IdentifierNode(string name, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.Name = name;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitIdentifier(this);
    }
}

/// <summary>
/// Comment node
/// </summary>
public class CommentNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.Comment;
    public string Text { get; set; }
    public bool IsRem { get; set; } // true for REM comments, false for ' comments

    public CommentNode(string text, bool isRem = false, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.Text = text;
        this.IsRem = isRem;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitComment(this);
    }
}

/// <summary>
/// Parameter modifiers
/// </summary>
public enum ParameterModifier
{
    None,
    ByVal,
    ByRef
}