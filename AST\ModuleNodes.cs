using System.Collections.Generic;

namespace VbDeobf.AST;

/// <summary>
/// Module node - represents a complete VBA module
/// </summary>
public class ModuleNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.Module;
    public ModuleHeaderNode? Header { get; set; }
    public ModuleDeclarationsNode Declarations { get; set; }
    public ModuleBodyNode Body { get; set; }
    public List<AttributeNode> Attributes { get; set; } = new List<AttributeNode>();
    public string? FileName { get; set; }

    public ModuleNode(ModuleDeclarationsNode declarations, ModuleBodyNode body, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Declarations = declarations;
        this.Body = body;
        this.AddChild(declarations);
        this.AddChild(body);
    }

    public void SetHeader(ModuleHeaderNode header)
    {
        this.Header = header;
        this.AddChild(header);
    }

    public void AddAttribute(AttributeNode attribute)
    {
        this.Attributes.Add(attribute);
        this.AddChild(attribute);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitModule(this);
    }
}

/// <summary>
/// Module header node - contains version and class information
/// </summary>
public class ModuleHeaderNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.ModuleHeader;
    public string Version { get; set; } = "1.0";
    public bool IsClass { get; set; }

    public ModuleHeaderNode(string version = "1.0", bool isClass = false, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Version = version;
        this.IsClass = isClass;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitModuleHeader(this);
    }
}

/// <summary>
/// Module declarations node - contains all module-level declarations
/// </summary>
public class ModuleDeclarationsNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.ModuleDeclarations;
    public List<DeclarationNode> Declarations { get; set; } = new List<DeclarationNode>();
    public List<ModuleOptionNode> Options { get; set; } = new List<ModuleOptionNode>();
    public List<DeclareStatementNode> DeclareStatements { get; set; } = new List<DeclareStatementNode>();

    public ModuleDeclarationsNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }

    public void AddDeclaration(DeclarationNode declaration)
    {
        this.Declarations.Add(declaration);
        this.AddChild(declaration);
    }

    public void AddOption(ModuleOptionNode option)
    {
        this.Options.Add(option);
        this.AddChild(option);
    }

    public void AddDeclareStatement(DeclareStatementNode declareStatement)
    {
        this.DeclareStatements.Add(declareStatement);
        this.AddChild(declareStatement);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitModuleDeclarations(this);
    }
}

/// <summary>
/// Module body node - contains all executable code
/// </summary>
public class ModuleBodyNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.ModuleBody;
    public List<DeclarationNode> Procedures { get; set; } = new List<DeclarationNode>();

    public ModuleBodyNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }

    public void AddProcedure(DeclarationNode procedure)
    {
        this.Procedures.Add(procedure);
        this.AddChild(procedure);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitModuleBody(this);
    }
}

/// <summary>
/// Module option node (Option Explicit, Option Base, etc.)
/// </summary>
public class ModuleOptionNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.Identifier; // Reusing identifier type
    public ModuleOptionType OptionType { get; set; }
    public string? Value { get; set; }

    public ModuleOptionNode(ModuleOptionType optionType, string? value = null, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.OptionType = optionType;
        this.Value = value;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitIdentifier(new IdentifierNode($"Option{this.OptionType}", this.SourceLocation));
    }
}

/// <summary>
/// Declare statement node for external function declarations
/// </summary>
public class DeclareStatementNode : DeclarationNode
{
    public override ASTNodeType NodeType => ASTNodeType.FunctionDeclaration; // Reusing function declaration type
    public bool IsFunction { get; set; } // true for Function, false for Sub
    public bool IsPtrSafe { get; set; }
    public string LibraryName { get; set; }
    public string? Alias { get; set; }
    public ParameterListNode Parameters { get; set; }
    public TypeNode? ReturnType { get; set; }
    public bool IsCDecl { get; set; }

    public DeclareStatementNode(string name, bool isFunction, string libraryName, ParameterListNode parameters,
        TypeNode? returnType = null, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.Name = name;
        this.IsFunction = isFunction;
        this.LibraryName = libraryName;
        this.Parameters = parameters;
        this.ReturnType = returnType;
        this.AddChild(parameters);
        if (returnType != null)
        {
            this.AddChild(returnType);
        }
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitFunctionDeclaration(
        new FunctionDeclarationNode(this.Name, this.Parameters, new BlockNode(), this.ReturnType, this.SourceLocation));
    }
}

/// <summary>
/// Module configuration reference node
/// </summary>
public class ModuleConfigReferenceNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.Identifier; // Reusing identifier type
    public string ObjectName { get; set; }
    public string ObjectValue { get; set; }
    public string ComponentValue { get; set; }

    public ModuleConfigReferenceNode(string objectName, string objectValue, string componentValue, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.ObjectName = objectName;
        this.ObjectValue = objectValue;
        this.ComponentValue = componentValue;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitIdentifier(new IdentifierNode(this.ObjectName, this.SourceLocation));
    }
}

/// <summary>
/// Module configuration property node
/// </summary>
public class ModuleConfigPropertyNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.Identifier; // Reusing identifier type
    public string PropertyName { get; set; }
    public string PropertyValue { get; set; }

    public ModuleConfigPropertyNode(string propertyName, string propertyValue, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.PropertyName = propertyName;
        this.PropertyValue = propertyValue;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitIdentifier(new IdentifierNode(this.PropertyName, this.SourceLocation));
    }
}

/// <summary>
/// Implements statement node
/// </summary>
public class ImplementsStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.CallStatement; // Reusing call statement type
    public ExpressionNode InterfaceName { get; set; }

    public ImplementsStatementNode(ExpressionNode interfaceName, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.InterfaceName = interfaceName;
        this.AddChild(interfaceName);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitCallStatement(new CallStatementNode(this.InterfaceName, false, this.SourceLocation));
    }
}

/// <summary>
/// DefType statement node (DefInt, DefStr, etc.)
/// </summary>
public class DefTypeStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.CallStatement; // Reusing call statement type
    public DefTypeType DefType { get; set; }
    public List<LetterRangeNode> LetterRanges { get; set; } = new List<LetterRangeNode>();

    public DefTypeStatementNode(DefTypeType defType, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.DefType = defType;
    }

    public void AddLetterRange(LetterRangeNode letterRange)
    {
        this.LetterRanges.Add(letterRange);
        this.AddChild(letterRange);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitCallStatement(
        new CallStatementNode(new IdentifierExpressionNode($"Def{this.DefType}"), false, this.SourceLocation));
    }
}

/// <summary>
/// Letter range node for DefType statements
/// </summary>
public class LetterRangeNode : ASTNodeBase
{
    public override ASTNodeType NodeType => ASTNodeType.Identifier; // Reusing identifier type
    public char StartLetter { get; set; }
    public char? EndLetter { get; set; }

    public LetterRangeNode(char startLetter, char? endLetter = null, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.StartLetter = startLetter;
        this.EndLetter = endLetter;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitIdentifier(
        new IdentifierNode(this.EndLetter.HasValue ? $"{this.StartLetter}-{this.EndLetter}" : this.StartLetter.ToString(), this.SourceLocation));
    }
}

/// <summary>
/// Module option types
/// </summary>
public enum ModuleOptionType
{
    Explicit,
    Base,
    Compare,
    Private
}

/// <summary>
/// DefType types
/// </summary>
public enum DefTypeType
{
    Bool,
    Byte,
    Int,
    Lng,
    LngLng,
    LngPtr,
    Cur,
    Sng,
    Dbl,
    Date,
    Str,
    Obj,
    Var
}