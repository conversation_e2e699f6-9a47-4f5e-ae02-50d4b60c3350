using System.Collections.Generic;

namespace VbDeobf.AST;

/// <summary>
/// Assignment statement node (Let, Set)
/// </summary>
public class AssignmentStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.AssignmentStatement;
    public ExpressionNode Target { get; set; }
    public ExpressionNode Value { get; set; }
    public bool IsSetAssignment { get; set; } // true for Set, false for Let

    public AssignmentStatementNode(ExpressionNode target, ExpressionNode value, bool isSetAssignment = false, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Target = target;
        this.Value = value;
        this.IsSetAssignment = isSetAssignment;
        this.AddChild(target);
        this.AddChild(value);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitAssignmentStatement(this);
    }
}

/// <summary>
/// Call statement node
/// </summary>
public class CallStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.CallStatement;
    public ExpressionNode Expression { get; set; }
    public bool HasCallKeyword { get; set; }

    public CallStatementNode(ExpressionNode expression, bool hasCallKeyword = false, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Expression = expression;
        this.HasCallKeyword = hasCallKeyword;
        this.AddChild(expression);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitCallStatement(this);
    }
}

/// <summary>
/// If statement node
/// </summary>
public class IfStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.IfStatement;
    public ExpressionNode Condition { get; set; }
    public BlockNode ThenBlock { get; set; }
    public List<ElseIfStatementNode> ElseIfBlocks { get; set; } = new List<ElseIfStatementNode>();
    public ElseStatementNode? ElseBlock { get; set; }
    public bool IsSingleLine { get; set; }

    public IfStatementNode(ExpressionNode condition, BlockNode thenBlock, bool isSingleLine = false, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Condition = condition;
        this.ThenBlock = thenBlock;
        this.IsSingleLine = isSingleLine;
        this.AddChild(condition);
        this.AddChild(thenBlock);
    }

    public void AddElseIf(ElseIfStatementNode elseIfBlock)
    {
        this.ElseIfBlocks.Add(elseIfBlock);
        this.AddChild(elseIfBlock);
    }

    public void SetElse(ElseStatementNode elseBlock)
    {
        this.ElseBlock = elseBlock;
        this.AddChild(elseBlock);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitIfStatement(this);
    }
}

/// <summary>
/// ElseIf statement node
/// </summary>
public class ElseIfStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.ElseIfStatement;
    public ExpressionNode Condition { get; set; }
    public BlockNode Block { get; set; }

    public ElseIfStatementNode(ExpressionNode condition, BlockNode block, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Condition = condition;
        this.Block = block;
        this.AddChild(condition);
        this.AddChild(block);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitElseIfStatement(this);
    }
}

/// <summary>
/// Else statement node
/// </summary>
public class ElseStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.ElseStatement;
    public BlockNode Block { get; set; }

    public ElseStatementNode(BlockNode block, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.Block = block;
        this.AddChild(block);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitElseStatement(this);
    }
}

/// <summary>
/// For statement node
/// </summary>
public class ForStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.ForStatement;
    public ExpressionNode Variable { get; set; }
    public ExpressionNode StartValue { get; set; }
    public ExpressionNode EndValue { get; set; }
    public ExpressionNode? StepValue { get; set; }
    public BlockNode Body { get; set; }

    public ForStatementNode(ExpressionNode variable, ExpressionNode startValue, ExpressionNode endValue,
        BlockNode body, ExpressionNode? stepValue = null, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Variable = variable;
        this.StartValue = startValue;
        this.EndValue = endValue;
        this.StepValue = stepValue;
        this.Body = body;
        this.AddChild(variable);
        this.AddChild(startValue);
        this.AddChild(endValue);
        if (stepValue != null)
        {
            this.AddChild(stepValue);
        }

        this.AddChild(body);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitForStatement(this);
    }
}

/// <summary>
/// For Each statement node
/// </summary>
public class ForEachStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.ForEachStatement;
    public ExpressionNode Variable { get; set; }
    public ExpressionNode Collection { get; set; }
    public BlockNode Body { get; set; }

    public ForEachStatementNode(ExpressionNode variable, ExpressionNode collection, BlockNode body, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Variable = variable;
        this.Collection = collection;
        this.Body = body;
        this.AddChild(variable);
        this.AddChild(collection);
        this.AddChild(body);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitForEachStatement(this);
    }
}

/// <summary>
/// While statement node
/// </summary>
public class WhileStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.WhileStatement;
    public ExpressionNode Condition { get; set; }
    public BlockNode Body { get; set; }

    public WhileStatementNode(ExpressionNode condition, BlockNode body, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Condition = condition;
        this.Body = body;
        this.AddChild(condition);
        this.AddChild(body);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitWhileStatement(this);
    }
}

/// <summary>
/// Do Loop statement node
/// </summary>
public class DoLoopStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.DoLoopStatement;
    public ExpressionNode? Condition { get; set; }
    public BlockNode Body { get; set; }
    public DoLoopType LoopType { get; set; }
    public bool IsConditionAtStart { get; set; }

    public DoLoopStatementNode(BlockNode body, DoLoopType loopType, ExpressionNode? condition = null,
        bool isConditionAtStart = true, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Body = body;
        this.LoopType = loopType;
        this.Condition = condition;
        this.IsConditionAtStart = isConditionAtStart;
        this.AddChild(body);
        if (condition != null)
        {
            this.AddChild(condition);
        }
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitDoLoopStatement(this);
    }
}

/// <summary>
/// Select Case statement node
/// </summary>
public class SelectCaseStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.SelectCaseStatement;
    public ExpressionNode Expression { get; set; }
    public List<CaseStatementNode> Cases { get; set; } = new List<CaseStatementNode>();
    public BlockNode? ElseCase { get; set; }

    public SelectCaseStatementNode(ExpressionNode expression, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Expression = expression;
        this.AddChild(expression);
    }

    public void AddCase(CaseStatementNode caseNode)
    {
        this.Cases.Add(caseNode);
        this.AddChild(caseNode);
    }

    public void SetElseCase(BlockNode elseCase)
    {
        this.ElseCase = elseCase;
        this.AddChild(elseCase);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitSelectCaseStatement(this);
    }
}

/// <summary>
/// Case statement node
/// </summary>
public class CaseStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.CaseStatement;
    public List<ExpressionNode> Values { get; set; } = new List<ExpressionNode>();
    public BlockNode Body { get; set; }
    public bool IsElseCase { get; set; }

    public CaseStatementNode(BlockNode body, bool isElseCase = false, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Body = body;
        this.IsElseCase = isElseCase;
        this.AddChild(body);
    }

    public void AddValue(ExpressionNode value)
    {
        this.Values.Add(value);
        this.AddChild(value);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitCaseStatement(this);
    }
}

/// <summary>
/// With statement node
/// </summary>
public class WithStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.WithStatement;
    public ExpressionNode Expression { get; set; }
    public BlockNode Body { get; set; }

    public WithStatementNode(ExpressionNode expression, BlockNode body, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.Expression = expression;
        this.Body = body;
        this.AddChild(expression);
        this.AddChild(body);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitWithStatement(this);
    }
}

/// <summary>
/// Exit statement node
/// </summary>
public class ExitStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.ExitStatement;
    public ExitType ExitType { get; set; }

    public ExitStatementNode(ExitType exitType, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.ExitType = exitType;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitExitStatement(this);
    }
}

/// <summary>
/// Return statement node
/// </summary>
public class ReturnStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.ReturnStatement;

    public ReturnStatementNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitReturnStatement(this);
    }
}

/// <summary>
/// GoTo statement node
/// </summary>
public class GoToStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.GoToStatement;
    public string Label { get; set; }

    public GoToStatementNode(string label, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.Label = label;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitGoToStatement(this);
    }
}

/// <summary>
/// GoSub statement node
/// </summary>
public class GoSubStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.GoSubStatement;
    public string Label { get; set; }

    public GoSubStatementNode(string label, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.Label = label;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitGoSubStatement(this);
    }
}

/// <summary>
/// On Error statement node
/// </summary>
public class OnErrorStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.OnErrorStatement;
    public OnErrorType ErrorType { get; set; }
    public string? Label { get; set; }

    public OnErrorStatementNode(OnErrorType errorType, string? label = null, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.ErrorType = errorType;
        this.Label = label;
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitOnErrorStatement(this);
    }
}

/// <summary>
/// RaiseEvent statement node
/// </summary>
public class RaiseEventStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.RaiseEventStatement;
    public string EventName { get; set; }
    public ArgumentListNode? Arguments { get; set; }

    public RaiseEventStatementNode(string eventName, ArgumentListNode? arguments = null, SourceLocation? sourceLocation = null)
        : base(sourceLocation)
    {
        this.EventName = eventName;
        this.Arguments = arguments;
        if (arguments != null)
        {
            this.AddChild(arguments);
        }
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitRaiseEventStatement(this);
    }
}

/// <summary>
/// Redim statement node
/// </summary>
public class RedimStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.RedimStatement;
    public List<ExpressionNode> Variables { get; set; } = new List<ExpressionNode>();
    public bool IsPreserve { get; set; }

    public RedimStatementNode(bool isPreserve = false, SourceLocation? sourceLocation = null) : base(sourceLocation)
    {
        this.IsPreserve = isPreserve;
    }

    public void AddVariable(ExpressionNode variable)
    {
        this.Variables.Add(variable);
        this.AddChild(variable);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitRedimStatement(this);
    }
}

/// <summary>
/// Erase statement node
/// </summary>
public class EraseStatementNode : StatementNode
{
    public override ASTNodeType NodeType => ASTNodeType.EraseStatement;
    public List<ExpressionNode> Variables { get; set; } = new List<ExpressionNode>();

    public EraseStatementNode(SourceLocation? sourceLocation = null) : base(sourceLocation) { }

    public void AddVariable(ExpressionNode variable)
    {
        this.Variables.Add(variable);
        this.AddChild(variable);
    }

    public override T Accept<T>(IASTVisitor<T> visitor)
    {
        return visitor.VisitEraseStatement(this);
    }
}

/// <summary>
/// Types of Do Loop statements
/// </summary>
public enum DoLoopType
{
    DoLoop,      // Do ... Loop (infinite)
    DoWhile,     // Do While ... Loop or Do ... Loop While
    DoUntil      // Do Until ... Loop or Do ... Loop Until
}

/// <summary>
/// Types of Exit statements
/// </summary>
public enum ExitType
{
    ExitDo,
    ExitFor,
    ExitFunction,
    ExitProperty,
    ExitSub
}

/// <summary>
/// Types of On Error statements
/// </summary>
public enum OnErrorType
{
    GoTo,
    Resume,
    ResumeNext
}