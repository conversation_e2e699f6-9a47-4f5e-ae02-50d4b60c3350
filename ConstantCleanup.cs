﻿using Antlr4.Runtime;
using Antlr4.Runtime.Misc;
using System.Diagnostics;
using static Utils;
using static VBAParser;

public class NumberLiteralRewriterVisitor : VBAParserBaseVisitor<object>, IRewriterVisitor
{
    public TokenStreamRewriter Rewriter { get; set; }

    public override object VisitAddOp([NotNull] AddOpContext context)
    {
        var l = TestStatementType<NumberLiteralContext>(context.GetChild(0));
        var r = TestStatementType<NumberLiteralContext>(context.GetChild(4));
        var op = context.GetChild(2).GetText();
        if (l != null && r != null && op is "+" or "-")
        {
            this.Rewriter.Replace(context.Start.TokenIndex, context.Stop.TokenIndex, this.EvalLiteral(l, r, op).ToString());
        }

        return base.VisitAddOp(context);
    }

    public override object VisitMultOp([NotNull] MultOpContext context)
    {
        var l = TestStatementType<NumberLiteralContext>(context.GetChild(0));
        var r = TestStatementType<NumberLiteralContext>(context.GetChild(4));
        var op = context.GetChild(2).GetText();
        if (l != null && r != null && op is "*" or "/")
        {
            this.Rewriter.Replace(context.Start.TokenIndex, context.Stop.TokenIndex, this.EvalLiteral(l, r, op).ToString());
        }

        return base.VisitMultOp(context);
    }

    public int EvalLiteral([NotNull] NumberLiteralContext left, [NotNull] NumberLiteralContext right, string op)
    {
        return op switch
        {
            "+" => this.Literal(left) + this.Literal(right),
            "-" => this.Literal(left) - this.Literal(right),
            "*" => this.Literal(left) * this.Literal(right),
            "/" => this.Literal(left) / this.Literal(right),
            "%" => this.Literal(left) % this.Literal(right),
            _ => throw new NotImplementedException()
        };
    }

    public int Literal([NotNull] NumberLiteralContext left)
    {
        return left.GetText().StartsWith("&H")
            ? int.Parse(left.GetText()[2..], System.Globalization.NumberStyles.HexNumber)
            : int.Parse(left.GetText());
    }
}

public class StringRewriterVisitor : VBAParserBaseVisitor<object>, IRewriterVisitor
{
    private TokenStreamRewriter _rewriter;
    public TokenStreamRewriter Rewriter
    {
        get
        {
            this.Rewrite();
            return this._rewriter;
        }
        set => this._rewriter = value;
    }
    public void Rewrite()
    {
    }
}