﻿// java -jar antlr-4.13.1-complete.jar -Dlanguage=CSharp -visitor -no-listener VBAParser.g4 VBALexer.g4

using Antlr4.Runtime;
using System.Text.RegularExpressions;
using VbDeobf.AST;

ASTExample.RunAllExamples();

// Original deobfuscation logic
var src = File.ReadAllText("demo.txt");
var dict = new Dictionary<string, string>();
var pass1 = Regex.Replace(src, "[—─┤┘┐L└│┌_┬]{2,}", m => dict.ContainsKey(m.Value) ? dict[m.Value] : dict[m.Value] = $"deobf{dict.Count}");
var pass2 = WithVisitor<object, LoopRewriterVisitor>(pass1, new());
var pass3 = WithVisitor<object, NumberLiteralRewriterVisitor>(pass2, new());
//var nr = new NameRewriterVisitor();
//var pass2 = WithVisitor<object, NameRewriterVisitor>(pass1, nr);
Console.WriteLine(pass3);

Console.WriteLine("\n=== AST-based Analysis ===");
try
{
    // Also demonstrate AST-based analysis on the deobfuscated code
    var ast = ASTExample.ParseVBACode(pass3, "deobfuscated.bas");
    var stats = ASTUtilities.GetStatistics(ast);
    Console.WriteLine(stats.ToString());
}
catch (Exception ex)
{
    Console.WriteLine($"AST analysis failed: {ex.Message}");
}

string WithVisitor<T, K>(string code, K visitor) where K : VBAParserBaseVisitor<T>, IRewriterVisitor
{
    var tokenStream = new CommonTokenStream(new VBALexer(new AntlrInputStream(code)));
    var parser = new VBAParser(tokenStream);
    var tree = parser.startRule();
    visitor.Rewriter = new TokenStreamRewriter(tokenStream);
    visitor.Visit(tree);
    return visitor.Rewriter.GetText();
}