using Antlr4.Runtime;
using Antlr4.Runtime.Tree;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace VbDeobf.AST;

/// <summary>
/// Visitor that converts ANTLR CST to custom AST
/// </summary>
public class CSTToASTVisitor : VBAParserBaseVisitor<IASTNode>
{
    private readonly string? _fileName;

    public CSTToASTVisitor(string? fileName = null)
    {
        this._fileName = fileName;
    }

    /// <summary>
    /// Create source location from parse tree context
    /// </summary>
    private SourceLocation CreateSourceLocation(IParseTree context)
    {
        if (context is ParserRuleContext ruleContext)
        {
            var start = ruleContext.Start;
            var stop = ruleContext.Stop ?? start;
            return SourceLocation.FromTokens(start, stop, this._fileName);
        }
        return new SourceLocation(0, 0, 0, 0, this._fileName);
    }

    /// <summary>
    /// Visit start rule - entry point
    /// </summary>
    public override IASTNode VisitStartRule(VBAParser.StartRuleContext context)
    {
        return this.Visit(context.module());
    }

    /// <summary>
    /// Visit module
    /// </summary>
    public override IASTNode VisitModule(VBAParser.ModuleContext context)
    {
        var declarations = new ModuleDeclarationsNode(this.CreateSourceLocation(context));
        var body = new ModuleBodyNode(this.CreateSourceLocation(context));
        var module = new ModuleNode(declarations, body, this.CreateSourceLocation(context));

        // Process module header if present
        if (context.moduleHeader() != null)
        {
            var header = (ModuleHeaderNode) this.Visit(context.moduleHeader());
            module.SetHeader(header);
        }

        // Process module declarations
        if (context.moduleDeclarations() != null)
        {
            var declsNode = (ModuleDeclarationsNode) this.Visit(context.moduleDeclarations());
            module.Declarations = declsNode;
        }

        // Process module body
        if (context.moduleBody() != null)
        {
            var bodyNode = (ModuleBodyNode) this.Visit(context.moduleBody());
            module.Body = bodyNode;
        }

        return module;
    }

    /// <summary>
    /// Visit module header
    /// </summary>
    public override IASTNode VisitModuleHeader(VBAParser.ModuleHeaderContext context)
    {
        var version = context.numberLiteral()?.GetText() ?? "1.0";
        var isClass = context.CLASS() != null;
        return new ModuleHeaderNode(version, isClass, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit module declarations
    /// </summary>
    public override IASTNode VisitModuleDeclarations(VBAParser.ModuleDeclarationsContext context)
    {
        var declarations = new ModuleDeclarationsNode(this.CreateSourceLocation(context));

        foreach (var element in context.moduleDeclarationsElement())
        {
            var node = this.Visit(element);
            if (node is DeclarationNode decl)
            {
                declarations.AddDeclaration(decl);
            }
            else if (node is ModuleOptionNode option)
            {
                declarations.AddOption(option);
            }
            else if (node is DeclareStatementNode declare)
            {
                declarations.AddDeclareStatement(declare);
            }
        }

        return declarations;
    }

    /// <summary>
    /// Visit module declarations element
    /// </summary>
    public override IASTNode VisitModuleDeclarationsElement(VBAParser.ModuleDeclarationsElementContext context)
    {
        // Handle different types of module-level declarations
        if (context.moduleVariableStmt() != null)
        {
            return this.Visit(context.moduleVariableStmt());
        }
        else if (context.moduleConstStmt() != null)
        {
            return this.Visit(context.moduleConstStmt());
        }
        else if (context.declareStmt() != null)
        {
            return this.Visit(context.declareStmt());
        }
        else if (context.enumerationStmt() != null)
        {
            return this.Visit(context.enumerationStmt());
        }
        else if (context.udtDeclaration() != null)
        {
            return this.Visit(context.udtDeclaration());
        }
        else if (context.eventStmt() != null)
        {
            return this.Visit(context.eventStmt());
        }

        // Handle module options
        return context.moduleOption() != null
            ? this.Visit(context.moduleOption())
            :  new IdentifierNode("UnknownDeclaration", this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit module body
    /// </summary>
    public override IASTNode VisitModuleBody(VBAParser.ModuleBodyContext context)
    {
        var body = new ModuleBodyNode(this.CreateSourceLocation(context));

        foreach (var element in context.moduleBodyElement())
        {
            var node = this.Visit(element);
            if (node is DeclarationNode procedure)
            {
                body.AddProcedure(procedure);
            }
        }

        return body;
    }

    /// <summary>
    /// Visit module body element
    /// </summary>
    public override IASTNode VisitModuleBodyElement(VBAParser.ModuleBodyElementContext context)
    {
        // Handle different types of procedures and declarations in module body
        if (context.functionStmt() != null)
        {
            return this.Visit(context.functionStmt());
        }
        else if (context.subStmt() != null)
        {
            return this.Visit(context.subStmt());
        }
        else if (context.propertyGetStmt() != null)
        {
            return this.Visit(context.propertyGetStmt());
        }
        else if (context.propertyLetStmt() != null)
        {
            return this.Visit(context.propertyLetStmt());
        }
        else if (context.propertySetStmt() != null)
        {
            return this.Visit(context.propertySetStmt());
        }

        return new IdentifierNode("UnknownProcedure", this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit function statement
    /// </summary>
    public override IASTNode VisitFunctionStmt(VBAParser.FunctionStmtContext context)
    {
        var name = context.functionName().GetText();
        var parameters = context.argList() != null ?
            (ParameterListNode) this.Visit(context.argList()) :
            new ParameterListNode(this.CreateSourceLocation(context));

        var body = context.block() != null ?
            (BlockNode) this.Visit(context.block()) :
            new BlockNode(this.CreateSourceLocation(context));

        TypeNode? returnType = null;
        if (context.asTypeClause() != null)
        {
            returnType = (TypeNode) this.Visit(context.asTypeClause());
        }

        var function = new FunctionDeclarationNode(name, parameters, body, returnType, this.CreateSourceLocation(context));

        // Handle visibility - check for PUBLIC, PRIVATE, FRIEND keywords
        if (context.visibility() != null)
        {
            function.Visibility = this.GetVisibilityModifier(context.visibility().GetText());
        }
        else
        {
            // Check if PUBLIC appears before the function
            var tokens = context.GetTokens(VBALexer.PUBLIC);
            if (tokens.Any())
            {
                function.Visibility = VisibilityModifier.Public;
            }
            else
            {
                tokens = context.GetTokens(VBALexer.PRIVATE);
                if (tokens.Any())
                {
                    function.Visibility = VisibilityModifier.Private;
                }
            }
        }

        // Handle static
        function.IsStatic = context.STATIC() != null;

        return function;
    }

    /// <summary>
    /// Visit subroutine statement
    /// </summary>
    public override IASTNode VisitSubStmt(VBAParser.SubStmtContext context)
    {
        var name = context.subroutineName().GetText();
        var parameters = context.argList() != null ?
            (ParameterListNode) this.Visit(context.argList()) :
            new ParameterListNode(this.CreateSourceLocation(context));

        var body = context.block() != null ?
            (BlockNode) this.Visit(context.block()) :
            new BlockNode(this.CreateSourceLocation(context));

        var subroutine = new SubroutineDeclarationNode(name, parameters, body, this.CreateSourceLocation(context));

        // Handle visibility - check for PUBLIC, PRIVATE, FRIEND keywords
        if (context.visibility() != null)
        {
            subroutine.Visibility = this.GetVisibilityModifier(context.visibility().GetText());
        }
        else
        {
            // Check if PUBLIC appears before the subroutine
            var tokens = context.GetTokens(VBALexer.PUBLIC);
            if (tokens.Any())
            {
                subroutine.Visibility = VisibilityModifier.Public;
            }
            else
            {
                tokens = context.GetTokens(VBALexer.PRIVATE);
                if (tokens.Any())
                {
                    subroutine.Visibility = VisibilityModifier.Private;
                }
            }
        }

        // Handle static
        subroutine.IsStatic = context.STATIC() != null;

        return subroutine;
    }

    /// <summary>
    /// Visit property statement
    /// </summary>
    public override IASTNode VisitPropertyGetStmt(VBAParser.PropertyGetStmtContext context)
    {
        var name = context.functionName().GetText();
        var parameters = context.argList() != null ?
            (ParameterListNode) this.Visit(context.argList()) :
            new ParameterListNode(this.CreateSourceLocation(context));

        var body = context.block() != null ?
            (BlockNode) this.Visit(context.block()) :
            new BlockNode(this.CreateSourceLocation(context));

        TypeNode? returnType = null;
        if (context.asTypeClause() != null)
        {
            returnType = (TypeNode) this.Visit(context.asTypeClause());
        }

        var property = new PropertyDeclarationNode(name, PropertyType.Get, parameters, body, returnType, this.CreateSourceLocation(context));

        // Handle visibility
        if (context.visibility() != null)
        {
            property.Visibility = this.GetVisibilityModifier(context.visibility().GetText());
        }

        return property;
    }

    /// <summary>
    /// Visit argument list
    /// </summary>
    public override IASTNode VisitArgList(VBAParser.ArgListContext context)
    {
        var paramList = new ParameterListNode(this.CreateSourceLocation(context));

        foreach (var arg in context.arg())
        {
            var param = (ParameterNode) this.Visit(arg);
            paramList.AddParameter(param);
        }

        return paramList;
    }

    /// <summary>
    /// Visit argument
    /// </summary>
    public override IASTNode VisitArg(VBAParser.ArgContext context)
    {
        var name = context.unrestrictedIdentifier().GetText();

        TypeNode? type = null;
        if (context.asTypeClause() != null)
        {
            type = (TypeNode) this.Visit(context.asTypeClause());
        }

        ExpressionNode? defaultValue = null;
        if (context.argDefaultValue() != null)
        {
            defaultValue = (ExpressionNode) this.Visit(context.argDefaultValue().expression());
        }

        var param = new ParameterNode(name, type, defaultValue, this.CreateSourceLocation(context));

        // Handle modifiers
        if (context.OPTIONAL() != null)
        {
            param.IsOptional = true;
        }

        if (context.BYVAL() != null)
        {
            param.Modifier = ParameterModifier.ByVal;
        }
        else if (context.BYREF() != null)
        {
            param.Modifier = ParameterModifier.ByRef;
        }

        if (context.PARAMARRAY() != null)
        {
            param.IsParamArray = true;
        }

        return param;
    }

    /// <summary>
    /// Visit block
    /// </summary>
    public override IASTNode VisitBlock(VBAParser.BlockContext context)
    {
        var block = new BlockNode(this.CreateSourceLocation(context));

        foreach (var stmt in context.blockStmt())
        {
            var statement = this.Visit(stmt);
            if (statement is StatementNode statementNode)
            {
                block.AddStatement(statementNode);
            }
            else if (statement != null)
            {
                // If it's not a statement node, create a comment statement
                var comment = new CommentNode($"Non-statement in block: {statement.GetType().Name}", false, this.CreateSourceLocation(stmt));
                block.AddStatement(comment);
            }
        }

        return block;
    }

    /// <summary>
    /// Visit block statement
    /// </summary>
    public override IASTNode VisitBlockStmt(VBAParser.BlockStmtContext context)
    {
        // Handle different types of statements
        if (context.mainBlockStmt() != null)
        {
            var result = this.Visit(context.mainBlockStmt());
            // Ensure we return a StatementNode
            if (result is StatementNode stmt)
            {
                return stmt;
            }
            // If it's not a statement node, wrap it in a comment
            return new CommentNode($"Non-statement: {result?.GetType().Name}", false, this.CreateSourceLocation(context));
        }
        else if (context.statementLabelDefinition() != null)
        {
            // For now, just return a comment node for labels
            return new CommentNode($"Label: {context.statementLabelDefinition().GetText()}", false, this.CreateSourceLocation(context));
        }

        return new CommentNode("Unknown statement", false, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit main block statement
    /// </summary>
    public override IASTNode VisitMainBlockStmt(VBAParser.MainBlockStmtContext context)
    {
        // Handle all the different statement types
        if (context.letStmt() != null)
        {
            return this.Visit(context.letStmt());
        }
        else if (context.setStmt() != null)
        {
            return this.Visit(context.setStmt());
        }
        else if (context.callStmt() != null)
        {
            return this.Visit(context.callStmt());
        }
        else if (context.ifStmt() != null)
        {
            return this.Visit(context.ifStmt());
        }
        else if (context.forNextStmt() != null)
        {
            return this.Visit(context.forNextStmt());
        }
        else if (context.forEachStmt() != null)
        {
            return this.Visit(context.forEachStmt());
        }
        else if (context.whileWendStmt() != null)
        {
            return this.Visit(context.whileWendStmt());
        }
        else if (context.doLoopStmt() != null)
        {
            return this.Visit(context.doLoopStmt());
        }
        else if (context.selectCaseStmt() != null)
        {
            return this.Visit(context.selectCaseStmt());
        }
        else if (context.variableStmt() != null)
        {
            return this.Visit(context.variableStmt());
        }
        else if (context.constStmt() != null)
        {
            return this.Visit(context.constStmt());
        }
        else if (context.exitStmt() != null)
        {
            return this.Visit(context.exitStmt());
        }

        return new CommentNode("Unknown main block statement", false, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Get visibility modifier from text
    /// </summary>
    private VisibilityModifier GetVisibilityModifier(string text)
    {
        return text.ToUpperInvariant() switch
        {
            "PUBLIC" => VisibilityModifier.Public,
            "PRIVATE" => VisibilityModifier.Private,
            "FRIEND" => VisibilityModifier.Friend,
            "GLOBAL" => VisibilityModifier.Global,
            _ => VisibilityModifier.Public
        };
    }

    /// <summary>
    /// Visit parenthesized expression
    /// </summary>
    public override IASTNode VisitParenthesizedExpr(VBAParser.ParenthesizedExprContext context)
    {
        var expr = (ExpressionNode) this.Visit(context.expression());
        return new ParenthesizedExpressionNode(expr, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit new expression
    /// </summary>
    public override IASTNode VisitNewExpr(VBAParser.NewExprContext context)
    {
        var type = (ExpressionNode) this.Visit(context.expression());
        return new NewExpressionNode(type, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit typeof expression
    /// </summary>
    public override IASTNode VisitTypeofexpr(VBAParser.TypeofexprContext context)
    {
        var expr = (ExpressionNode) this.Visit(context.expression());
        return new TypeOfExpressionNode(expr, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit lExpr (left-hand side expressions)
    /// </summary>
    public override IASTNode VisitLExpr(VBAParser.LExprContext context)
    {
        return this.Visit(context.lExpression());
    }

    /// <summary>
    /// Visit add operation
    /// </summary>
    public override IASTNode VisitAddOp(VBAParser.AddOpContext context)
    {
        var left = (ExpressionNode) this.Visit(context.expression(0));
        var right = (ExpressionNode) this.Visit(context.expression(1));
        var op = context.PLUS() != null ? BinaryOperator.Add : BinaryOperator.Subtract;
        return new BinaryExpressionNode(left, op, right, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit multiply operation
    /// </summary>
    public override IASTNode VisitMultOp(VBAParser.MultOpContext context)
    {
        var left = (ExpressionNode) this.Visit(context.expression(0));
        var right = (ExpressionNode) this.Visit(context.expression(1));
        var op = context.MULT() != null ? BinaryOperator.Multiply : BinaryOperator.Divide;
        return new BinaryExpressionNode(left, op, right, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit concatenation operation
    /// </summary>
    public override IASTNode VisitConcatOp(VBAParser.ConcatOpContext context)
    {
        var left = (ExpressionNode) this.Visit(context.expression(0));
        var right = (ExpressionNode) this.Visit(context.expression(1));
        return new BinaryExpressionNode(left, BinaryOperator.Concatenate, right, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit relational operation
    /// </summary>
    public override IASTNode VisitRelationalOp(VBAParser.RelationalOpContext context)
    {
        var left = (ExpressionNode) this.Visit(context.expression(0));
        var right = (ExpressionNode) this.Visit(context.expression(1));
        var op = this.GetRelationalOperator(context);
        return new BinaryExpressionNode(left, op, right, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit logical AND operation
    /// </summary>
    public override IASTNode VisitLogicalAndOp(VBAParser.LogicalAndOpContext context)
    {
        var left = (ExpressionNode) this.Visit(context.expression(0));
        var right = (ExpressionNode) this.Visit(context.expression(1));
        return new BinaryExpressionNode(left, BinaryOperator.And, right, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit logical OR operation
    /// </summary>
    public override IASTNode VisitLogicalOrOp(VBAParser.LogicalOrOpContext context)
    {
        var left = (ExpressionNode) this.Visit(context.expression(0));
        var right = (ExpressionNode) this.Visit(context.expression(1));
        return new BinaryExpressionNode(left, BinaryOperator.Or, right, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit unary minus operation
    /// </summary>
    public override IASTNode VisitUnaryMinusOp(VBAParser.UnaryMinusOpContext context)
    {
        var operand = (ExpressionNode) this.Visit(context.expression());
        return new UnaryExpressionNode(UnaryOperator.Minus, operand, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit logical NOT operation
    /// </summary>
    public override IASTNode VisitLogicalNotOp(VBAParser.LogicalNotOpContext context)
    {
        var operand = (ExpressionNode) this.Visit(context.expression());
        return new UnaryExpressionNode(UnaryOperator.Not, operand, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit literal expression
    /// </summary>
    public override IASTNode VisitLiteralExpression(VBAParser.LiteralExpressionContext context)
    {
        if (context.STRINGLITERAL() != null)
        {
            var value = context.STRINGLITERAL().GetText();
            // Remove quotes
            value = value[1..^1];
            return new StringLiteralNode(value, this.CreateSourceLocation(context));
        }
        else if (context.numberLiteral() != null)
        {
            return this.VisitNumberLiteral(context.numberLiteral());
        }
        else if (context.DATELITERAL() != null)
        {
            var dateText = context.DATELITERAL().GetText();
            // Remove # symbols and parse date
            dateText = dateText[1..^1];
            return DateTime.TryParse(dateText, out var date)
                ?  new DateLiteralNode(date, this.CreateSourceLocation(context))
                :  new StringLiteralNode(dateText, this.CreateSourceLocation(context));
        }
        else if (context.literalIdentifier() != null)
        {
            return this.VisitLiteralIdentifier(context.literalIdentifier());
        }

        return new StringLiteralNode("", this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit number literal
    /// </summary>
    public override IASTNode VisitNumberLiteral(VBAParser.NumberLiteralContext context)
    {
        var text = context.GetText();

        // Try to parse as different number types
        if (int.TryParse(text, out var intValue))
        {
            return new NumberLiteralNode(intValue, NumberType.Integer, this.CreateSourceLocation(context));
        }
        else if (long.TryParse(text, out var longValue))
        {
            return new NumberLiteralNode(longValue, NumberType.Long, this.CreateSourceLocation(context));
        }
        else if (double.TryParse(text, NumberStyles.Float, CultureInfo.InvariantCulture, out var doubleValue))
        {
            return new NumberLiteralNode(doubleValue, NumberType.Double, this.CreateSourceLocation(context));
        }

        return new NumberLiteralNode(0, NumberType.Integer, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit literal identifier (True, False, Nothing, etc.)
    /// </summary>
    public override IASTNode VisitLiteralIdentifier(VBAParser.LiteralIdentifierContext context)
    {
        var text = context.GetText().ToUpperInvariant();

        return text switch
        {
            "TRUE" => new BooleanLiteralNode(true, this.CreateSourceLocation(context)),
            "FALSE" => new BooleanLiteralNode(false, this.CreateSourceLocation(context)),
            "NOTHING" => new NothingLiteralNode(this.CreateSourceLocation(context)),
            "EMPTY" => new EmptyLiteralNode(this.CreateSourceLocation(context)),
            "NULL" => new NullLiteralNode(this.CreateSourceLocation(context)),
            _ => new IdentifierExpressionNode(context.GetText(), this.CreateSourceLocation(context))
        };
    }

    /// <summary>
    /// Visit simple name expression
    /// </summary>
    public override IASTNode VisitSimpleNameExpr(VBAParser.SimpleNameExprContext context)
    {
        return new IdentifierExpressionNode(context.identifier().GetText(), this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit member access expression
    /// </summary>
    public override IASTNode VisitMemberAccessExpr(VBAParser.MemberAccessExprContext context)
    {
        var obj = (ExpressionNode) this.Visit(context.lExpression());
        var memberName = context.unrestrictedIdentifier().GetText();
        return new MemberAccessExpressionNode(obj, memberName, false, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit index expression
    /// </summary>
    public override IASTNode VisitIndexExpr(VBAParser.IndexExprContext context)
    {
        var obj = (ExpressionNode) this.Visit(context.lExpression());
        var args = context.argumentList() != null ?
            (ArgumentListNode) this.Visit(context.argumentList()) :
            new ArgumentListNode(this.CreateSourceLocation(context));
        return new IndexExpressionNode(obj, args, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit dictionary access expression
    /// </summary>
    public override IASTNode VisitDictionaryAccessExpr(VBAParser.DictionaryAccessExprContext context)
    {
        var obj = (ExpressionNode) this.Visit(context.lExpression());
        var memberName = context.unrestrictedIdentifier().GetText();
        return new MemberAccessExpressionNode(obj, memberName, true, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit instance expression (Me)
    /// </summary>
    public override IASTNode VisitInstanceExpr(VBAParser.InstanceExprContext context)
    {
        return new IdentifierExpressionNode("Me", this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Get relational operator from context
    /// </summary>
    private BinaryOperator GetRelationalOperator(VBAParser.RelationalOpContext context)
    {
        if (context.EQ() != null)
        {
            return BinaryOperator.Equal;
        }

        if (context.NEQ() != null)
        {
            return BinaryOperator.NotEqual;
        }

        if (context.LT() != null)
        {
            return BinaryOperator.LessThan;
        }

        if (context.LEQ() != null)
        {
            return BinaryOperator.LessThanOrEqual;
        }

        if (context.GT() != null)
        {
            return BinaryOperator.GreaterThan;
        }

        if (context.GEQ() != null)
        {
            return BinaryOperator.GreaterThanOrEqual;
        }

        if (context.LIKE() != null)
        {
            return BinaryOperator.Like;
        }

        if (context.IS() != null)
        {
            return BinaryOperator.Is;
        }

        return BinaryOperator.Equal; // Default
    }

    /// <summary>
    /// Visit assignment statement (Let/Set)
    /// </summary>
    public override IASTNode VisitLetStmt(VBAParser.LetStmtContext context)
    {
        var target = (ExpressionNode) this.Visit(context.lExpression());
        var value = (ExpressionNode) this.Visit(context.expression());
        var isSetAssignment = false; // Let statements are not Set assignments

        return new AssignmentStatementNode(target, value, isSetAssignment, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit Set statement
    /// </summary>
    public override IASTNode VisitSetStmt(VBAParser.SetStmtContext context)
    {
        var target = (ExpressionNode) this.Visit(context.lExpression());
        var value = (ExpressionNode) this.Visit(context.expression());

        return new AssignmentStatementNode(target, value, true, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit call statement
    /// </summary>
    public override IASTNode VisitCallStmt(VBAParser.CallStmtContext context)
    {
        var hasCallKeyword = context.CALL() != null;
        var expression = (ExpressionNode) this.Visit(context.lExpression());

        // If there are arguments, create a call expression
        if (context.argumentList() != null)
        {
            var args = (ArgumentListNode) this.Visit(context.argumentList());
            expression = new CallExpressionNode(expression, args, this.CreateSourceLocation(context));
        }

        return new CallStatementNode(expression, hasCallKeyword, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit If statement
    /// </summary>
    public override IASTNode VisitIfStmt(VBAParser.IfStmtContext context)
    {
        var condition = (ExpressionNode) this.Visit(context.booleanExpression());

        // Get the main block
        var thenBlock = context.block() != null ?
            (BlockNode) this.Visit(context.block()) :
            new BlockNode(this.CreateSourceLocation(context));

        var ifStmt = new IfStatementNode(condition, thenBlock, false, this.CreateSourceLocation(context));

        // Handle ElseIf blocks
        if (context.elseIfBlock() != null)
        {
            foreach (var elseIfCtx in context.elseIfBlock())
            {
                var elseIfCondition = (ExpressionNode) this.Visit(elseIfCtx.booleanExpression());
                var elseIfBlock = elseIfCtx.block() != null ?
                    (BlockNode) this.Visit(elseIfCtx.block()) :
                    new BlockNode(this.CreateSourceLocation(elseIfCtx));

                var elseIfStmt = new ElseIfStatementNode(elseIfCondition, elseIfBlock, this.CreateSourceLocation(elseIfCtx));
                ifStmt.AddElseIf(elseIfStmt);
            }
        }

        // Handle Else block
        if (context.elseBlock() != null)
        {
            var elseBlock = context.elseBlock().block() != null ?
                (BlockNode) this.Visit(context.elseBlock().block()) :
                new BlockNode(this.CreateSourceLocation(context.elseBlock()));

            var elseStmt = new ElseStatementNode(elseBlock, this.CreateSourceLocation(context.elseBlock()));
            ifStmt.SetElse(elseStmt);
        }

        return ifStmt;
    }

    /// <summary>
    /// Visit For statement
    /// </summary>
    public override IASTNode VisitForNextStmt(VBAParser.ForNextStmtContext context)
    {
        var variable = (ExpressionNode) this.Visit(context.expression(0));
        var startValue = (ExpressionNode) this.Visit(context.expression(1));
        var endValue = (ExpressionNode) this.Visit(context.expression(2));

        ExpressionNode? stepValue = null;
        if (context.stepStmt() != null)
        {
            stepValue = (ExpressionNode) this.Visit(context.stepStmt().expression());
        }

        var body = context.unterminatedBlock() != null ?
            (BlockNode) this.Visit(context.unterminatedBlock()) :
            new BlockNode(this.CreateSourceLocation(context));

        return new ForStatementNode(variable, startValue, endValue, body, stepValue, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit For Each statement
    /// </summary>
    public override IASTNode VisitForEachStmt(VBAParser.ForEachStmtContext context)
    {
        var variable = (ExpressionNode) this.Visit(context.expression(0));
        var collection = (ExpressionNode) this.Visit(context.expression(1));

        var body = context.unterminatedBlock() != null ?
            (BlockNode) this.Visit(context.unterminatedBlock()) :
            new BlockNode(this.CreateSourceLocation(context));

        return new ForEachStatementNode(variable, collection, body, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit While statement
    /// </summary>
    public override IASTNode VisitWhileWendStmt(VBAParser.WhileWendStmtContext context)
    {
        var condition = (ExpressionNode) this.Visit(context.expression());
        var body = context.block() != null ?
            (BlockNode) this.Visit(context.block()) :
            new BlockNode(this.CreateSourceLocation(context));

        return new WhileStatementNode(condition, body, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit Do Loop statement
    /// </summary>
    public override IASTNode VisitDoLoopStmt(VBAParser.DoLoopStmtContext context)
    {
        var body = context.block() != null ?
            (BlockNode) this.Visit(context.block()) :
            new BlockNode(this.CreateSourceLocation(context));

        ExpressionNode? condition = null;
        var loopType = DoLoopType.DoLoop;
        var isConditionAtStart = true;

        // Check for While/Until conditions
        if (context.WHILE() != null)
        {
            loopType = DoLoopType.DoWhile;
            if (context.expression() != null)
            {
                condition = (ExpressionNode) this.Visit(context.expression());
            }
        }
        else if (context.UNTIL() != null)
        {
            loopType = DoLoopType.DoUntil;
            if (context.expression() != null)
            {
                condition = (ExpressionNode) this.Visit(context.expression());
            }
        }

        return new DoLoopStatementNode(body, loopType, condition, isConditionAtStart, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit argument list
    /// </summary>
    public override IASTNode VisitArgumentList(VBAParser.ArgumentListContext context)
    {
        var argList = new ArgumentListNode(this.CreateSourceLocation(context));

        foreach (var arg in context.argument())
        {
            var argNode = (ArgumentNode) this.Visit(arg);
            argList.AddArgument(argNode);
        }

        return argList;
    }

    /// <summary>
    /// Visit argument
    /// </summary>
    public override IASTNode VisitArgument(VBAParser.ArgumentContext context)
    {
        if (context.positionalArgument() != null)
        {
            var expr = (ExpressionNode) this.Visit(context.positionalArgument().argumentExpression());
            return new ArgumentNode(expr, null, this.CreateSourceLocation(context));
        }
        else if (context.namedArgument() != null)
        {
            var name = context.namedArgument().unrestrictedIdentifier().GetText();
            var expr = (ExpressionNode) this.Visit(context.namedArgument().argumentExpression());
            return new ArgumentNode(expr, name, this.CreateSourceLocation(context));
        }
        else if (context.missingArgument() != null)
        {
            var emptyExpr = new EmptyLiteralNode(this.CreateSourceLocation(context));
            var arg = new ArgumentNode(emptyExpr, null, this.CreateSourceLocation(context))
            {
                IsMissing = true
            };
            return arg;
        }

        return new ArgumentNode(new EmptyLiteralNode(this.CreateSourceLocation(context)), null, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit variable statement
    /// </summary>
    public override IASTNode VisitVariableStmt(VBAParser.VariableStmtContext context)
    {
        // For now, return the first variable declaration
        // In a full implementation, you'd handle multiple variables in one statement
        return context.variableListStmt() != null
            ? this.Visit(context.variableListStmt())
            :  new VariableDeclarationNode("UnknownVar", null, null, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit variable list statement
    /// </summary>
    public override IASTNode VisitVariableListStmt(VBAParser.VariableListStmtContext context)
    {
        // Return the first variable for simplicity
        return context.variableSubStmt().Length > 0
            ? this.Visit(context.variableSubStmt(0))
            :  new VariableDeclarationNode("UnknownVar", null, null, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit variable sub statement
    /// </summary>
    public override IASTNode VisitVariableSubStmt(VBAParser.VariableSubStmtContext context)
    {
        var name = context.identifier().GetText();

        TypeNode? type = null;
        if (context.asTypeClause() != null)
        {
            type = (TypeNode) this.Visit(context.asTypeClause());
        }

        var variable = new VariableDeclarationNode(name, type, null, this.CreateSourceLocation(context));

        // Handle WithEvents
        if (context.WITHEVENTS() != null)
        {
            variable.IsWithEvents = true;
        }

        return variable;
    }

    /// <summary>
    /// Visit constant statement
    /// </summary>
    public override IASTNode VisitConstStmt(VBAParser.ConstStmtContext context)
    {
        // Return the first constant for simplicity
        return context.constSubStmt().Length > 0
            ? this.Visit(context.constSubStmt(0))
            :  new ConstantDeclarationNode("UnknownConst", new StringLiteralNode("", this.CreateSourceLocation(context)), null, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit constant sub statement
    /// </summary>
    public override IASTNode VisitConstSubStmt(VBAParser.ConstSubStmtContext context)
    {
        var name = context.identifier().GetText();
        var value = (ExpressionNode) this.Visit(context.expression());

        TypeNode? type = null;
        if (context.asTypeClause() != null)
        {
            type = (TypeNode) this.Visit(context.asTypeClause());
        }

        return new ConstantDeclarationNode(name, value, type, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit as type clause
    /// </summary>
    public override IASTNode VisitAsTypeClause(VBAParser.AsTypeClauseContext context)
    {
        return context.type() != null ? this.Visit(context.type()) :  new BuiltInTypeNode(VBAType.Variant, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit type
    /// </summary>
    public override IASTNode VisitType(VBAParser.TypeContext context)
    {
        if (context.baseType() != null)
        {
            return this.Visit(context.baseType());
        }
        else if (context.complexType() != null)
        {
            return this.Visit(context.complexType());
        }

        return new BuiltInTypeNode(VBAType.Variant, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Visit base type
    /// </summary>
    public override IASTNode VisitBaseType(VBAParser.BaseTypeContext context)
    {
        var typeText = context.GetText().ToUpperInvariant();

        var vbaType = typeText switch
        {
            "BOOLEAN" => VBAType.Boolean,
            "BYTE" => VBAType.Byte,
            "INTEGER" => VBAType.Integer,
            "LONG" => VBAType.Long,
            "LONGLONG" => VBAType.LongLong,
            "SINGLE" => VBAType.Single,
            "DOUBLE" => VBAType.Double,
            "CURRENCY" => VBAType.Currency,
            "DATE" => VBAType.Date,
            "STRING" => VBAType.String,
            "OBJECT" => VBAType.Object,
            "VARIANT" => VBAType.Variant,
            _ => VBAType.Variant
        };

        return new BuiltInTypeNode(vbaType, this.CreateSourceLocation(context));
    }

    /// <summary>
    /// Default visit - returns null for unhandled nodes
    /// </summary>
    protected override IASTNode DefaultResult => null!;
}