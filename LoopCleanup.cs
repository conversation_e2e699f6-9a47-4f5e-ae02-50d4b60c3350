﻿using Antlr4.Runtime;
using Antlr4.Runtime.Misc;
using static Utils;
using static VBAParser;

/// <summary>
///  val = n;
///  loop_start:
///  if val > k then goto break;
///  actual logic;
///  val = val + 1;
///  goto loop_start;
///  break:
///  nop;
/// </summary>
public class LoopRewriterVisitor : VBAParserBaseVisitor<object>, IRewriterVisitor
{
    public TokenStreamRewriter Rewriter { get; set; }
    public void OnLabel([NotNull] StatementLabelDefinitionContext context)
    {
        var ins = context.Parent.Parent;
        var currentIndex = IndexOf(ins, context.Parent);

        if (currentIndex == -1)
        {
            return;
        }

        var loopStartLabel = context.Start.Text;
        var next = ins.GetChild(currentIndex + 2);
        if (next is null)
        {
            return;
        }

        var ifthen = TestStatementType<IfWithNonEmptyThenContext>(next);
        if (ifthen is null)
        {
            return;
        }

        var prev = ins.GetChild(currentIndex - 2);
        var assigninit = TestStatementType<LetStmtContext>(prev);
        if (assigninit is null)
        {
            return;
        }

        var loopVar = assigninit.GetChild(0).GetText();
        var loopStartValue = assigninit.GetChild(4).GetText();

        var condExpr = BfsType<RelationalOpContext>(ifthen);
        var condExitGoto = BfsType<GoToStmtContext>(ifthen);

        if (condExpr is null || condExitGoto is null)
        {
            return;
        }

        if (loopVar != condExpr.GetChild(0).GetText())
        {
            // perhaps cfg required
            throw new NotImplementedException();
        }

        var loopBreakLabelName = BfsType<IdentifierContext>(condExitGoto)!.Start.Text;
        var loopDirection = condExpr.GetChild(2).GetText();
        var loopEndValue = condExpr.GetChild(4).GetText();

        StatementLabelDefinitionContext? loopBreakLabel = null;
        foreach (var lb in BfsTypeAll<StatementLabelDefinitionContext>(ins))
        {
            if (lb.Start.Text == loopBreakLabelName)
            {
                loopBreakLabel = lb;
                break;
            }
        }

        if (loopBreakLabel is null)
        {
            return;
        }

        var gotoStart = TestStatementType<GoToStmtContext>(ins.GetChild(IndexOf(ins, loopBreakLabel.Parent) - 2));
        var stepInst = TestStatementType<LetStmtContext>(ins.GetChild(IndexOf(ins, loopBreakLabel.Parent) - 4));

        if (gotoStart is null || stepInst is null)
        {
            return;
        }

        // TODO: impl STEP


        this.Rewriter.Replace(context.Start.TokenIndex, context.Stop.TokenIndex + 1, "");
        this.Rewriter.Replace(loopBreakLabel.Start.TokenIndex, loopBreakLabel.Stop.TokenIndex + 1, "");
        this.Rewriter.Replace(assigninit.Start.TokenIndex, assigninit.Stop.TokenIndex, "");
        this.Rewriter.Replace(ifthen.Start.TokenIndex, ifthen.Stop.TokenIndex, $"For {loopVar} = {loopStartValue} To {loopEndValue}");
        this.Rewriter.Replace(gotoStart.Start.TokenIndex, gotoStart.Stop.TokenIndex, $"Next {loopVar}");
        this.Rewriter.Replace(stepInst.Start.TokenIndex, stepInst.Stop.TokenIndex, "");
        foreach (var lb in BfsTypeAll<GoToStmtContext>(ins))
        {
            if (lb.Stop.Text == loopBreakLabelName && lb != condExitGoto)
            {
                this.Rewriter.Replace(lb.Start.TokenIndex, lb.Stop.TokenIndex, "Exit For");
            }
        }
    }

    public override object VisitStatementLabelDefinition([NotNull] StatementLabelDefinitionContext context)
    {
        this.OnLabel(context);
        return base.VisitStatementLabelDefinition(context);
    }
}