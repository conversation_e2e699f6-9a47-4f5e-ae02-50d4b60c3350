using System;
using System.Linq;
using System.Text;

namespace VbDeobf.AST;

/// <summary>
/// Generates VBA code from AST nodes
/// </summary>
public class VBACodeGenerator : BaseASTVisitor<string>
{
    private int _indentLevel = 0;
    private readonly string _indentString = "    "; // 4 spaces

    /// <summary>
    /// Generate code with proper indentation
    /// </summary>
    private string Indent(string code = "")
    {
        return new string(' ', this._indentLevel * this._indentString.Length) + code;
    }

    /// <summary>
    /// Increase indentation level
    /// </summary>
    private void IncreaseIndent()
    {
        this._indentLevel++;
    }

    /// <summary>
    /// Decrease indentation level
    /// </summary>
    private void DecreaseIndent()
    {
        this._indentLevel = Math.Max(0, this._indentLevel - 1);
    }

    /// <summary>
    /// Generate code for a module
    /// </summary>
    public override string VisitModule(ModuleNode node)
    {
        var sb = new StringBuilder();

        // Module header
        if (node.Header != null)
        {
            sb.AppendLine(node.Header.Accept(this));
        }

        // Module attributes
        foreach (var attr in node.Attributes)
        {
            sb.AppendLine(attr.Accept(this));
        }

        // Module declarations
        sb.AppendLine(node.Declarations.Accept(this));

        // Module body
        sb.AppendLine(node.Body.Accept(this));

        return sb.ToString();
    }

    /// <summary>
    /// Generate code for module header
    /// </summary>
    public override string VisitModuleHeader(ModuleHeaderNode node)
    {
        var result = $"VERSION {node.Version}";
        if (node.IsClass)
        {
            result += " CLASS";
        }
        return result;
    }

    /// <summary>
    /// Generate code for module declarations
    /// </summary>
    public override string VisitModuleDeclarations(ModuleDeclarationsNode node)
    {
        var sb = new StringBuilder();

        // Options
        foreach (var option in node.Options)
        {
            sb.AppendLine(option.Accept(this));
        }

        // Declare statements
        foreach (var declare in node.DeclareStatements)
        {
            sb.AppendLine(declare.Accept(this));
        }

        // Declarations
        foreach (var decl in node.Declarations)
        {
            sb.AppendLine(decl.Accept(this));
            sb.AppendLine(); // Add blank line after declarations
        }

        return sb.ToString();
    }

    /// <summary>
    /// Generate code for module body
    /// </summary>
    public override string VisitModuleBody(ModuleBodyNode node)
    {
        var sb = new StringBuilder();

        foreach (var procedure in node.Procedures)
        {
            sb.AppendLine(procedure.Accept(this));
            sb.AppendLine(); // Add blank line between procedures
        }

        return sb.ToString();
    }

    /// <summary>
    /// Generate code for function declaration
    /// </summary>
    public override string VisitFunctionDeclaration(FunctionDeclarationNode node)
    {
        var sb = new StringBuilder();

        // Function signature
        var signature = new StringBuilder();

        // Visibility - always show it
        signature.Append($"{node.Visibility} ");

        // Static
        if (node.IsStatic)
        {
            signature.Append("Static ");
        }

        signature.Append($"Function {node.Name}");
        signature.Append(node.Parameters.Accept(this));

        // Return type
        if (node.ReturnType != null)
        {
            signature.Append($" As {node.ReturnType.Accept(this)}");
        }

        sb.AppendLine(this.Indent(signature.ToString()));

        // Function body
        this.IncreaseIndent();
        sb.Append(node.Body.Accept(this));
        this.DecreaseIndent();

        // End Function
        sb.AppendLine(this.Indent("End Function"));

        return sb.ToString();
    }

    /// <summary>
    /// Generate code for subroutine declaration
    /// </summary>
    public override string VisitSubroutineDeclaration(SubroutineDeclarationNode node)
    {
        var sb = new StringBuilder();

        // Subroutine signature
        var signature = new StringBuilder();

        // Visibility - always show it
        signature.Append($"{node.Visibility} ");

        // Static
        if (node.IsStatic)
        {
            signature.Append("Static ");
        }

        signature.Append($"Sub {node.Name}");
        signature.Append(node.Parameters.Accept(this));

        sb.AppendLine(this.Indent(signature.ToString()));

        // Subroutine body
        this.IncreaseIndent();
        sb.Append(node.Body.Accept(this));
        this.DecreaseIndent();

        // End Sub
        sb.AppendLine(this.Indent("End Sub"));

        return sb.ToString();
    }

    /// <summary>
    /// Generate code for parameter list
    /// </summary>
    public override string VisitParameterList(ParameterListNode node)
    {
        if (node.Parameters.Count == 0)
        {
            return "()";
        }

        var parameters = node.Parameters.Select(p => p.Accept(this));
        return $"({string.Join(", ", parameters)})";
    }

    /// <summary>
    /// Generate code for parameter
    /// </summary>
    public override string VisitParameter(ParameterNode node)
    {
        var sb = new StringBuilder();

        if (node.IsOptional)
        {
            sb.Append("Optional ");
        }

        if (node.Modifier != ParameterModifier.None)
        {
            sb.Append($"{node.Modifier} ");
        }

        if (node.IsParamArray)
        {
            sb.Append("ParamArray ");
        }

        sb.Append(node.Name);

        if (node.Type != null)
        {
            sb.Append($" As {node.Type.Accept(this)}");
        }

        if (node.DefaultValue != null)
        {
            sb.Append($" = {node.DefaultValue.Accept(this)}");
        }

        return sb.ToString();
    }

    /// <summary>
    /// Generate code for block
    /// </summary>
    public override string VisitBlock(BlockNode node)
    {
        var sb = new StringBuilder();

        foreach (var statement in node.Statements)
        {
            sb.AppendLine(this.Indent(statement.Accept(this)));
        }

        return sb.ToString();
    }

    /// <summary>
    /// Generate code for assignment statement
    /// </summary>
    public override string VisitAssignmentStatement(AssignmentStatementNode node)
    {
        var keyword = node.IsSetAssignment ? "Set " : "";
        return $"{keyword}{node.Target.Accept(this)} = {node.Value.Accept(this)}";
    }

    /// <summary>
    /// Generate code for variable declaration
    /// </summary>
    public override string VisitVariableDeclaration(VariableDeclarationNode node)
    {
        var sb = new StringBuilder();

        // For module-level variables, show visibility, for local variables use Dim
        var isModuleLevel = node.Visibility != VisibilityModifier.Public || node.IsStatic;

        if (isModuleLevel)
        {
            // Module-level variable
            sb.Append($"{node.Visibility} ");
        }

        // Static
        if (node.IsStatic)
        {
            sb.Append("Static ");
        }

        // WithEvents
        if (node.IsWithEvents)
        {
            sb.Append("WithEvents ");
        }

        // Use Dim for local variables, or if it's not explicitly module-level
        if (!isModuleLevel || sb.Length == 0)
        {
            sb.Append("Dim ");
        }

        sb.Append(node.Name);

        // Array dimensions
        if (node.ArrayDimensions != null)
        {
            sb.Append("()"); // Simplified array notation
        }

        // Type
        if (node.Type != null)
        {
            sb.Append($" As {node.Type.Accept(this)}");
        }

        // Initializer
        if (node.Initializer != null)
        {
            sb.Append($" = {node.Initializer.Accept(this)}");
        }

        return sb.ToString();
    }

    /// <summary>
    /// Generate code for call statement
    /// </summary>
    public override string VisitCallStatement(CallStatementNode node)
    {
        var keyword = node.HasCallKeyword ? "Call " : "";
        return $"{keyword}{node.Expression.Accept(this)}";
    }

    /// <summary>
    /// Generate code for if statement
    /// </summary>
    public override string VisitIfStatement(IfStatementNode node)
    {
        var sb = new StringBuilder();

        sb.AppendLine($"If {node.Condition.Accept(this)} Then");

        this.IncreaseIndent();
        sb.Append(node.ThenBlock.Accept(this));
        this.DecreaseIndent();

        // ElseIf blocks
        foreach (var elseIf in node.ElseIfBlocks)
        {
            sb.AppendLine($"ElseIf {elseIf.Condition.Accept(this)} Then");
            this.IncreaseIndent();
            sb.Append(elseIf.Block.Accept(this));
            this.DecreaseIndent();
        }

        // Else block
        if (node.ElseBlock != null)
        {
            sb.AppendLine("Else");
            this.IncreaseIndent();
            sb.Append(node.ElseBlock.Block.Accept(this));
            this.DecreaseIndent();
        }

        sb.Append("End If");

        return sb.ToString();
    }

    /// <summary>
    /// Generate code for binary expression
    /// </summary>
    public override string VisitBinaryExpression(BinaryExpressionNode node)
    {
        var left = node.Left.Accept(this);
        var right = node.Right.Accept(this);
        var op = this.GetBinaryOperatorString(node.Operator);

        return $"{left} {op} {right}";
    }

    /// <summary>
    /// Generate code for unary expression
    /// </summary>
    public override string VisitUnaryExpression(UnaryExpressionNode node)
    {
        var operand = node.Operand.Accept(this);
        var op = this.GetUnaryOperatorString(node.Operator);

        return $"{op}{operand}";
    }

    /// <summary>
    /// Generate code for identifier expression
    /// </summary>
    public override string VisitIdentifierExpression(IdentifierExpressionNode node)
    {
        return node.Name;
    }

    /// <summary>
    /// Generate code for string literal
    /// </summary>
    public override string VisitStringLiteral(StringLiteralNode node)
    {
        return $"\"{node.Value}\"";
    }

    /// <summary>
    /// Generate code for number literal
    /// </summary>
    public override string VisitNumberLiteral(NumberLiteralNode node)
    {
        return node.Value.ToString() ?? "0";
    }

    /// <summary>
    /// Generate code for boolean literal
    /// </summary>
    public override string VisitBooleanLiteral(BooleanLiteralNode node)
    {
        return node.Value ? "True" : "False";
    }

    /// <summary>
    /// Get string representation of binary operator
    /// </summary>
    private string GetBinaryOperatorString(BinaryOperator op)
    {
        return op switch
        {
            BinaryOperator.Add => "+",
            BinaryOperator.Subtract => "-",
            BinaryOperator.Multiply => "*",
            BinaryOperator.Divide => "/",
            BinaryOperator.IntegerDivide => "\\",
            BinaryOperator.Modulo => "Mod",
            BinaryOperator.Power => "^",
            BinaryOperator.Concatenate => "&",
            BinaryOperator.Equal => "=",
            BinaryOperator.NotEqual => "<>",
            BinaryOperator.LessThan => "<",
            BinaryOperator.LessThanOrEqual => "<=",
            BinaryOperator.GreaterThan => ">",
            BinaryOperator.GreaterThanOrEqual => ">=",
            BinaryOperator.Like => "Like",
            BinaryOperator.Is => "Is",
            BinaryOperator.And => "And",
            BinaryOperator.Or => "Or",
            BinaryOperator.Xor => "Xor",
            BinaryOperator.Eqv => "Eqv",
            BinaryOperator.Imp => "Imp",
            _ => "+"
        };
    }

    /// <summary>
    /// Get string representation of unary operator
    /// </summary>
    private string GetUnaryOperatorString(UnaryOperator op)
    {
        return op switch
        {
            UnaryOperator.Plus => "+",
            UnaryOperator.Minus => "-",
            UnaryOperator.Not => "Not ",
            _ => ""
        };
    }

    /// <summary>
    /// Generate code for built-in type
    /// </summary>
    public override string VisitBuiltInType(BuiltInTypeNode node)
    {
        return node.Type switch
        {
            VBAType.Boolean => "Boolean",
            VBAType.Byte => "Byte",
            VBAType.Integer => "Integer",
            VBAType.Long => "Long",
            VBAType.LongLong => "LongLong",
            VBAType.Single => "Single",
            VBAType.Double => "Double",
            VBAType.Currency => "Currency",
            VBAType.Date => "Date",
            VBAType.String => "String",
            VBAType.Object => "Object",
            VBAType.Variant => "Variant",
            _ => "Variant"
        };
    }

    /// <summary>
    /// Generate code for user-defined type
    /// </summary>
    public override string VisitUserDefinedType(UserDefinedTypeNode node)
    {
        return node.TypeName;
    }

    /// <summary>
    /// Generate code for member access expression
    /// </summary>
    public override string VisitMemberAccessExpression(MemberAccessExpressionNode node)
    {
        var obj = node.Object.Accept(this);
        var separator = node.IsDictionaryAccess ? "!" : ".";
        return $"{obj}{separator}{node.MemberName}";
    }

    /// <summary>
    /// Generate code for index expression
    /// </summary>
    public override string VisitIndexExpression(IndexExpressionNode node)
    {
        var obj = node.Object.Accept(this);
        var args = node.Arguments.Accept(this);
        return $"{obj}{args}";
    }

    /// <summary>
    /// Generate code for call expression
    /// </summary>
    public override string VisitCallExpression(CallExpressionNode node)
    {
        var func = node.Function.Accept(this);
        var args = node.Arguments.Accept(this);
        return $"{func}{args}";
    }

    /// <summary>
    /// Generate code for argument list
    /// </summary>
    public override string VisitArgumentList(ArgumentListNode node)
    {
        if (node.Arguments.Count == 0)
        {
            return "()";
        }

        var arguments = node.Arguments.Select(a => a.Accept(this));
        return $"({string.Join(", ", arguments)})";
    }

    /// <summary>
    /// Generate code for argument
    /// </summary>
    public override string VisitArgument(ArgumentNode node)
    {
        var sb = new StringBuilder();

        if (node.IsByVal)
        {
            sb.Append("ByVal ");
        }

        if (!string.IsNullOrEmpty(node.Name))
        {
            sb.Append($"{node.Name}:=");
        }

        if (node.IsMissing)
        {
            sb.Append("");
        }
        else
        {
            sb.Append(node.Expression.Accept(this));
        }

        return sb.ToString();
    }

    /// <summary>
    /// Default visit returns empty string
    /// </summary>
    protected override string DefaultVisit(IASTNode node)
    {
        return "";
    }
}