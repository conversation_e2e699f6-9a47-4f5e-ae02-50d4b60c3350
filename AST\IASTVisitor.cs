namespace VbDeobf.AST;

/// <summary>
/// Visitor interface for traversing AST nodes
/// </summary>
/// <typeparam name="T">Return type of visit methods</typeparam>
public interface IASTVisitor<T>
{
    // Program structure
    T VisitModule(ModuleNode node);
    T VisitModuleHeader(ModuleHeaderNode node);
    T VisitModuleDeclarations(ModuleDeclarationsNode node);
    T VisitModuleBody(ModuleBodyNode node);

    // Declarations
    T VisitFunctionDeclaration(FunctionDeclarationNode node);
    T VisitSubroutineDeclaration(SubroutineDeclarationNode node);
    T VisitPropertyDeclaration(PropertyDeclarationNode node);
    T VisitVariableDeclaration(VariableDeclarationNode node);
    T VisitConstantDeclaration(ConstantDeclarationNode node);
    T VisitTypeDeclaration(TypeDeclarationNode node);
    T VisitEnumDeclaration(EnumDeclarationNode node);
    T VisitEventDeclaration(EventDeclarationNode node);

    // Statements
    T VisitAssignmentStatement(AssignmentStatementNode node);
    T VisitCallStatement(CallStatementNode node);
    T VisitIfStatement(IfStatementNode node);
    T VisitElseIfStatement(ElseIfStatementNode node);
    T VisitElseStatement(ElseStatementNode node);
    T VisitForStatement(ForStatementNode node);
    T VisitForEachStatement(ForEachStatementNode node);
    T VisitWhileStatement(WhileStatementNode node);
    T VisitDoLoopStatement(DoLoopStatementNode node);
    T VisitSelectCaseStatement(SelectCaseStatementNode node);
    T VisitCaseStatement(CaseStatementNode node);
    T VisitWithStatement(WithStatementNode node);
    T VisitExitStatement(ExitStatementNode node);
    T VisitReturnStatement(ReturnStatementNode node);
    T VisitGoToStatement(GoToStatementNode node);
    T VisitGoSubStatement(GoSubStatementNode node);
    T VisitOnErrorStatement(OnErrorStatementNode node);
    T VisitRaiseEventStatement(RaiseEventStatementNode node);
    T VisitRedimStatement(RedimStatementNode node);
    T VisitEraseStatement(EraseStatementNode node);

    // Expressions
    T VisitLiteralExpression(LiteralExpressionNode node);
    T VisitIdentifierExpression(IdentifierExpressionNode node);
    T VisitBinaryExpression(BinaryExpressionNode node);
    T VisitUnaryExpression(UnaryExpressionNode node);
    T VisitMemberAccessExpression(MemberAccessExpressionNode node);
    T VisitIndexExpression(IndexExpressionNode node);
    T VisitCallExpression(CallExpressionNode node);
    T VisitParenthesizedExpression(ParenthesizedExpressionNode node);
    T VisitTypeOfExpression(TypeOfExpressionNode node);
    T VisitNewExpression(NewExpressionNode node);
    T VisitAddressOfExpression(AddressOfExpressionNode node);

    // Literals
    T VisitStringLiteral(StringLiteralNode node);
    T VisitNumberLiteral(NumberLiteralNode node);
    T VisitBooleanLiteral(BooleanLiteralNode node);
    T VisitDateLiteral(DateLiteralNode node);
    T VisitNothingLiteral(NothingLiteralNode node);
    T VisitEmptyLiteral(EmptyLiteralNode node);
    T VisitNullLiteral(NullLiteralNode node);

    // Types
    T VisitBuiltInType(BuiltInTypeNode node);
    T VisitUserDefinedType(UserDefinedTypeNode node);
    T VisitArrayType(ArrayTypeNode node);

    // Parameters and Arguments
    T VisitParameter(ParameterNode node);
    T VisitParameterList(ParameterListNode node);
    T VisitArgument(ArgumentNode node);
    T VisitArgumentList(ArgumentListNode node);

    // Attributes and Modifiers
    T VisitAttribute(AttributeNode node);
    T VisitAttributeList(AttributeListNode node);
    T VisitVisibility(VisibilityNode node);

    // Misc
    T VisitBlock(BlockNode node);
    T VisitIdentifier(IdentifierNode node);
    T VisitComment(CommentNode node);
}

/// <summary>
/// Base visitor class that provides default implementations
/// </summary>
/// <typeparam name="T">Return type</typeparam>
public abstract class BaseASTVisitor<T> : IASTVisitor<T>
{
    /// <summary>
    /// Default visit method that can be overridden for common behavior
    /// </summary>
    /// <param name="node">Node to visit</param>
    /// <returns>Default result</returns>
    protected virtual T DefaultVisit(IASTNode node)
    {
        return default!;
    }

    /// <summary>
    /// Visit all children of a node and return results
    /// </summary>
    /// <param name="node">Parent node</param>
    /// <returns>Results from visiting all children</returns>
    protected virtual IEnumerable<T> VisitChildren(IASTNode node)
    {
        foreach (var child in node.Children)
        {
            yield return child.Accept(this);
        }
    }

    // Program structure
    public virtual T VisitModule(ModuleNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitModuleHeader(ModuleHeaderNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitModuleDeclarations(ModuleDeclarationsNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitModuleBody(ModuleBodyNode node)
    {
        return this.DefaultVisit(node);
    }

    // Declarations
    public virtual T VisitFunctionDeclaration(FunctionDeclarationNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitSubroutineDeclaration(SubroutineDeclarationNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitPropertyDeclaration(PropertyDeclarationNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitVariableDeclaration(VariableDeclarationNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitConstantDeclaration(ConstantDeclarationNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitTypeDeclaration(TypeDeclarationNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitEnumDeclaration(EnumDeclarationNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitEventDeclaration(EventDeclarationNode node)
    {
        return this.DefaultVisit(node);
    }

    // Statements
    public virtual T VisitAssignmentStatement(AssignmentStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitCallStatement(CallStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitIfStatement(IfStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitElseIfStatement(ElseIfStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitElseStatement(ElseStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitForStatement(ForStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitForEachStatement(ForEachStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitWhileStatement(WhileStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitDoLoopStatement(DoLoopStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitSelectCaseStatement(SelectCaseStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitCaseStatement(CaseStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitWithStatement(WithStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitExitStatement(ExitStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitReturnStatement(ReturnStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitGoToStatement(GoToStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitGoSubStatement(GoSubStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitOnErrorStatement(OnErrorStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitRaiseEventStatement(RaiseEventStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitRedimStatement(RedimStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitEraseStatement(EraseStatementNode node)
    {
        return this.DefaultVisit(node);
    }

    // Expressions
    public virtual T VisitLiteralExpression(LiteralExpressionNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitIdentifierExpression(IdentifierExpressionNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitBinaryExpression(BinaryExpressionNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitUnaryExpression(UnaryExpressionNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitMemberAccessExpression(MemberAccessExpressionNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitIndexExpression(IndexExpressionNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitCallExpression(CallExpressionNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitParenthesizedExpression(ParenthesizedExpressionNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitTypeOfExpression(TypeOfExpressionNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitNewExpression(NewExpressionNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitAddressOfExpression(AddressOfExpressionNode node)
    {
        return this.DefaultVisit(node);
    }

    // Literals
    public virtual T VisitStringLiteral(StringLiteralNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitNumberLiteral(NumberLiteralNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitBooleanLiteral(BooleanLiteralNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitDateLiteral(DateLiteralNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitNothingLiteral(NothingLiteralNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitEmptyLiteral(EmptyLiteralNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitNullLiteral(NullLiteralNode node)
    {
        return this.DefaultVisit(node);
    }

    // Types
    public virtual T VisitBuiltInType(BuiltInTypeNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitUserDefinedType(UserDefinedTypeNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitArrayType(ArrayTypeNode node)
    {
        return this.DefaultVisit(node);
    }

    // Parameters and Arguments
    public virtual T VisitParameter(ParameterNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitParameterList(ParameterListNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitArgument(ArgumentNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitArgumentList(ArgumentListNode node)
    {
        return this.DefaultVisit(node);
    }

    // Attributes and Modifiers
    public virtual T VisitAttribute(AttributeNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitAttributeList(AttributeListNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitVisibility(VisibilityNode node)
    {
        return this.DefaultVisit(node);
    }

    // Misc
    public virtual T VisitBlock(BlockNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitIdentifier(IdentifierNode node)
    {
        return this.DefaultVisit(node);
    }

    public virtual T VisitComment(CommentNode node)
    {
        return this.DefaultVisit(node);
    }
}