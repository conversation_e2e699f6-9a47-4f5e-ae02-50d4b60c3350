using Antlr4.Runtime;
using System.Collections.Generic;

namespace VbDeobf.AST;

/// <summary>
/// Base interface for all AST nodes
/// </summary>
public interface IASTNode
{
    /// <summary>
    /// The type of AST node
    /// </summary>
    ASTNodeType NodeType { get; }

    /// <summary>
    /// Parent node in the AST
    /// </summary>
    IASTNode? Parent { get; set; }

    /// <summary>
    /// Child nodes
    /// </summary>
    IReadOnlyList<IASTNode> Children { get; }

    /// <summary>
    /// Source location information from the original parse tree
    /// </summary>
    SourceLocation? SourceLocation { get; set; }

    /// <summary>
    /// Accept a visitor for traversal
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="visitor">The visitor</param>
    /// <returns>Result of visiting this node</returns>
    T Accept<T>(IASTVisitor<T> visitor);

    /// <summary>
    /// Add a child node
    /// </summary>
    /// <param name="child">Child to add</param>
    void AddChild(IASTNode child);

    /// <summary>
    /// Remove a child node
    /// </summary>
    /// <param name="child">Child to remove</param>
    /// <returns>True if removed successfully</returns>
    bool RemoveChild(IASTNode child);

    /// <summary>
    /// Replace a child node with another
    /// </summary>
    /// <param name="oldChild">Child to replace</param>
    /// <param name="newChild">New child</param>
    /// <returns>True if replaced successfully</returns>
    bool ReplaceChild(IASTNode oldChild, IASTNode newChild);

    /// <summary>
    /// Get a string representation of this node for debugging
    /// </summary>
    /// <returns>String representation</returns>
    string ToDebugString();
}

/// <summary>
/// Enumeration of all AST node types
/// </summary>
public enum ASTNodeType
{
    // Program structure
    Module,
    ModuleHeader,
    ModuleDeclarations,
    ModuleBody,

    // Declarations
    FunctionDeclaration,
    SubroutineDeclaration,
    PropertyDeclaration,
    VariableDeclaration,
    ConstantDeclaration,
    TypeDeclaration,
    EnumDeclaration,
    EventDeclaration,

    // Statements
    AssignmentStatement,
    CallStatement,
    IfStatement,
    ElseIfStatement,
    ElseStatement,
    ForStatement,
    ForEachStatement,
    WhileStatement,
    DoLoopStatement,
    SelectCaseStatement,
    CaseStatement,
    WithStatement,
    ExitStatement,
    ReturnStatement,
    GoToStatement,
    GoSubStatement,
    OnErrorStatement,
    RaiseEventStatement,
    RedimStatement,
    EraseStatement,

    // Expressions
    LiteralExpression,
    IdentifierExpression,
    BinaryExpression,
    UnaryExpression,
    MemberAccessExpression,
    IndexExpression,
    CallExpression,
    ParenthesizedExpression,
    TypeOfExpression,
    NewExpression,
    AddressOfExpression,

    // Literals
    StringLiteral,
    NumberLiteral,
    BooleanLiteral,
    DateLiteral,
    NothingLiteral,
    EmptyLiteral,
    NullLiteral,

    // Types
    BuiltInType,
    UserDefinedType,
    ArrayType,

    // Parameters and Arguments
    Parameter,
    ParameterList,
    Argument,
    ArgumentList,

    // Attributes and Modifiers
    Attribute,
    AttributeList,
    Visibility,

    // Misc
    Block,
    Identifier,
    Comment
}

/// <summary>
/// Source location information
/// </summary>
public class SourceLocation
{
    public int StartLine { get; set; }
    public int StartColumn { get; set; }
    public int EndLine { get; set; }
    public int EndColumn { get; set; }
    public string? FileName { get; set; }

    public SourceLocation(int startLine, int startColumn, int endLine, int endColumn, string? fileName = null)
    {
        this.StartLine = startLine;
        this.StartColumn = startColumn;
        this.EndLine = endLine;
        this.EndColumn = endColumn;
        this.FileName = fileName;
    }

    public static SourceLocation FromToken(IToken token, string? fileName = null)
    {
        return new SourceLocation(
            token.Line,
            token.Column,
            token.Line,
            token.Column + token.Text.Length,
            fileName
        );
    }

    public static SourceLocation FromTokens(IToken start, IToken end, string? fileName = null)
    {
        return new SourceLocation(
            start.Line,
            start.Column,
            end.Line,
            end.Column + end.Text.Length,
            fileName
        );
    }

    public override string ToString()
    {
        var file = string.IsNullOrEmpty(this.FileName) ? "" : $"{this.FileName}:";
        return $"{file}({this.StartLine},{this.StartColumn})-({this.EndLine},{this.EndColumn})";
    }
}